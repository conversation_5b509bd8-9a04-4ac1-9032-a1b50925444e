plugins {
    id 'java'
    id 'application'
}

mainClassName = 'com.itspazaz.rt4.Playground'
version = '1.0.0'

sourceCompatibility = 1.8
targetCompatibility = 1.8

compileJava.options.encoding = 'UTF-8'

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
    // Suppress warnings about deprecated Applet classes (expected for 2009scape client)
    options.compilerArgs += ['-Xlint:-removal', '-Xlint:-deprecation', '-Xlint:-unchecked']
}

dependencies {
    implementation project(':client')
}

jar {
    manifest {
        attributes 'Main-Class': "$mainClassName"
    }
    from { configurations.compileClasspath.collect { it.isDirectory() ? it : zipTree(it) } }
    duplicatesStrategy = DuplicatesStrategy.INCLUDE
}
