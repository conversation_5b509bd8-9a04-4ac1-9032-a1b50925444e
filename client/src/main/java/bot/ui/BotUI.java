package bot.ui;

import bot.core.ScriptManager;
import bot.core.script.ExternalScriptManager;
import bot.core.script.ScriptLoadEvent;
import bot.core.script.ScriptLoadListener;
// Old dashboard panel removed - using new paint system
import bot.ui.script.ScriptBrowserPanel;
import bot.util.logging.LoggerFactory;
import bot.impl.rendering.debug.DebugModeManager;
import bot.util.game.world.Tile;
import bot.api.script.BotScript;
import bot.util.event.EventBus;

import bot.util.game.GameState;
import bot.util.game.entity.Player;
import bot.util.logging.Logger;
import bot.ui.settings.Setting;
import bot.ui.settings.SettingCategory;
import bot.ui.settings.SettingChangeListener;
import bot.ui.settings.SettingsManager;

import javax.swing.*;

import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.*;
import java.util.HashMap;
import java.util.Map;
import java.io.File;
import java.io.IOException;

import java.net.URL;
import java.net.URLClassLoader;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.net.URLDecoder;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * Main class for launching bot scripts
 */
public class BotUI implements ScriptLoadListener {
    private JPanel mainPanel;
    private JTextArea logTextArea;
    private JTable statsTable;
    private DefaultTableModel statsTableModel;

    private Thread updateThread;
    private JProgressBar progressBar;
    private JLabel statusLabel;
    private JTabbedPane tabbedPane;
    private bot.ui.paint.PaintDashboard dashboardPanel;

    // Settings variables
    private int scriptLoopDelay;
    private boolean autoAdjustCamera;

    // Settings change listener
    private final SettingChangeListener settingChangeListener = this::handleSettingChange;

    // List of available scripts
    private List<ScriptInfo> availableScripts = new ArrayList<>();

    // Track the last number of log entries to avoid unnecessary updates
    private int lastLogEntryCount = 0;
    private static final Logger logger = LoggerFactory.getLogger("BotUI");

    /**
     * Creates a new BotUI
     */
    public BotUI() {
        // These components are already initialized by BotClient
        // We don't need to initialize them again here
        // SettingsManager.initialize();
        // ModernUI.initialize();
        // TaskQueue.getInstance().start();
        // AccountManager.getInstance().loadAccounts();

        // Load settings
        scriptLoopDelay = SettingsManager.getScriptLoopDelay();
        autoAdjustCamera = SettingsManager.getAutoAdjustCamera();

        // Register settings change listener
        SettingsManager.addListener(settingChangeListener);

        // Register as script load listener
        ExternalScriptManager.getInstance().addListener(this);

        // Create the main panel
        mainPanel = ModernUI.createPanel(new BorderLayout(), ModernUI.PADDING_MEDIUM);

        // Create the tabbed pane
        tabbedPane = ModernUI.createTabbedPane();

        // Create the new paint dashboard panel
        dashboardPanel = new bot.ui.paint.PaintDashboard();
        tabbedPane.addTab("Dashboard", dashboardPanel);

        // Connect dashboard to paint manager
        bot.ui.paint.PaintManager.getInstance().setDashboard(dashboardPanel);

        // Create the script browser panel
        ScriptBrowserPanel scriptBrowserPanel = new ScriptBrowserPanel();
        tabbedPane.addTab("Scripts", scriptBrowserPanel);

        // Create the log panel
        JPanel logPanel = createLogPanel();
        tabbedPane.addTab("Log", logPanel);

        // Create the stats panel
        JPanel statsPanel = createStatsPanel();
        tabbedPane.addTab("Stats", statsPanel);

        // Create the account manager panel
        JPanel accountManagerPanel = new bot.ui.account.AccountManagerPanel();
        tabbedPane.addTab("Accounts", accountManagerPanel);

        // Create the settings panel
        JPanel settingsPanel = createSettingsPanel();
        tabbedPane.addTab("Settings", settingsPanel);

        // Add the tabbed pane to the main panel
        mainPanel.add(tabbedPane, BorderLayout.CENTER);

        // Create the status panel
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);

        // Load the scripts
        loadScripts();

        // Update the script browser panel with the loaded scripts
        scriptBrowserPanel.updateScripts(availableScripts);

        // Start the update thread
        startUpdateThread();

    }



    /**
     * Creates the log panel
     *
     * @return The log panel
     */
    private JPanel createLogPanel() {
        JPanel panel = ModernUI.createPanel(new BorderLayout(), ModernUI.PADDING_MEDIUM);

        // Create the log text area
        logTextArea = new JTextArea();
        logTextArea.setEditable(false);
        logTextArea.setFont(new Font("Monospaced", Font.PLAIN, 12));
        logTextArea.setBackground(ModernUI.PRIMARY_COLOR);
        logTextArea.setForeground(ModernUI.TEXT_COLOR);
        logTextArea.setBorder(BorderFactory.createEmptyBorder(ModernUI.PADDING_SMALL, ModernUI.PADDING_SMALL, ModernUI.PADDING_SMALL, ModernUI.PADDING_SMALL));

        // Create the scroll pane
        JScrollPane scrollPane = ModernUI.createScrollPane(logTextArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Creates the stats panel
     *
     * @return The stats panel
     */
    private JPanel createStatsPanel() {
        JPanel panel = ModernUI.createPanel(new BorderLayout(), ModernUI.PADDING_MEDIUM);

        // Create the stats table model
        statsTableModel = new DefaultTableModel(new Object[][] {}, new Object[] {"Stat", "Value"}) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        // Create the stats table
        statsTable = new JTable(statsTableModel);
        statsTable.setFont(ModernUI.NORMAL_FONT);
        statsTable.setRowHeight(25);
        statsTable.setShowGrid(false);
        statsTable.setIntercellSpacing(new Dimension(0, 0));
        statsTable.getTableHeader().setFont(ModernUI.HEADER_FONT);
        statsTable.getTableHeader().setBackground(ModernUI.SECONDARY_COLOR);
        statsTable.getTableHeader().setForeground(ModernUI.TEXT_COLOR);
        statsTable.getTableHeader().setBorder(BorderFactory.createMatteBorder(0, 0, 1, 0, ModernUI.PRIMARY_COLOR));
        statsTable.setSelectionBackground(ModernUI.ACCENT_COLOR);
        statsTable.setSelectionForeground(ModernUI.TEXT_COLOR);
        statsTable.setBackground(ModernUI.PRIMARY_COLOR);
        statsTable.setForeground(ModernUI.TEXT_COLOR);

        // Create the scroll pane
        JScrollPane scrollPane = ModernUI.createScrollPane(statsTable);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Creates the debug panel - DEPRECATED: Functionality moved to settings
     * This method is kept for backward compatibility but is no longer used
     *
     * @return The debug panel
     */
    private JPanel createDebugPanel() {
        JPanel panel = ModernUI.createPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM));

        // Create the debug content panel
        JPanel contentPanel = ModernUI.createPanel(new GridLayout(0, 1, 0, ModernUI.PADDING_MEDIUM));
        contentPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));

        // Create the debug mode panel
        JPanel debugModePanel = ModernUI.createPanel(new BorderLayout());
        debugModePanel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(ModernUI.PRIMARY_COLOR),
                BorderFactory.createEmptyBorder(ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM)
        ));

        // Add debug mode title
        JLabel debugModeTitle = ModernUI.createHeaderLabel("Debug Mode");
        debugModePanel.add(debugModeTitle, BorderLayout.NORTH);

        // Create the debug mode checkbox
        JCheckBox debugModeCheckBox = new JCheckBox("Enable Debug Mode");
        debugModeCheckBox.setFont(ModernUI.NORMAL_FONT);
        debugModeCheckBox.setBackground(ModernUI.SECONDARY_COLOR);
        debugModeCheckBox.setForeground(ModernUI.TEXT_COLOR);
        debugModeCheckBox.setFocusPainted(false);
        debugModeCheckBox.setSelected(SettingsManager.getDebugModeEnabled());
        debugModeCheckBox.addActionListener(e -> {
            boolean enabled = debugModeCheckBox.isSelected();
            DebugModeManager.setDebugMode(enabled);
            SettingsManager.setDebugModeEnabled(enabled);
        });
        debugModePanel.add(debugModeCheckBox, BorderLayout.NORTH);

        // Create the entity type panel
        JPanel entityTypePanel = ModernUI.createPanel(new FlowLayout(FlowLayout.LEFT));
        entityTypePanel.setBorder(BorderFactory.createEmptyBorder(ModernUI.PADDING_SMALL, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_SMALL, ModernUI.PADDING_SMALL));

        // Create the entity type label
        JLabel entityTypeLabel = ModernUI.createLabel("Entity Type:");
        entityTypePanel.add(entityTypeLabel);

        // Create the entity type combo box
        JComboBox<DebugModeManager.EntityType> entityTypeComboBox = new JComboBox<>(DebugModeManager.EntityType.values());
        entityTypeComboBox.setFont(ModernUI.NORMAL_FONT);
        entityTypeComboBox.setBackground(ModernUI.PRIMARY_COLOR);
        entityTypeComboBox.setForeground(ModernUI.TEXT_COLOR);

        // First get the saved setting
        String savedEntityType = SettingsManager.getString(SettingsManager.SELECTED_ENTITY_TYPE.getKey(),
                                                         DebugModeManager.getInstance().getSelectedEntityType().name());
        try {
            DebugModeManager.EntityType type = DebugModeManager.EntityType.valueOf(savedEntityType);
            entityTypeComboBox.setSelectedItem(type);
            // Explicitly set the entity type in the manager
            DebugModeManager.getInstance().setSelectedEntityType(type);
        } catch (IllegalArgumentException ex) {
            // If invalid saved value, use the current debug manager state
            DebugModeManager.EntityType currentType = DebugModeManager.getInstance().getSelectedEntityType();
            entityTypeComboBox.setSelectedItem(currentType);
            DebugModeManager.getInstance().setSelectedEntityType(currentType);
        }

        entityTypeComboBox.addActionListener(e -> DebugModeManager.getInstance().setSelectedEntityType(
                (DebugModeManager.EntityType) entityTypeComboBox.getSelectedItem()));
        entityTypePanel.add(entityTypeComboBox);

        // Add the entity type panel to the debug mode panel
        debugModePanel.add(entityTypePanel, BorderLayout.CENTER);

        // Add the debug mode panel to the content panel
        contentPanel.add(debugModePanel);

        // Add the content panel to the debug panel
        panel.add(contentPanel, BorderLayout.NORTH);

        return panel;
    }

    /**
     * Creates the settings panel
     *
     * @return The settings panel
     */
    private JPanel createSettingsPanel() {
        JPanel panel = ModernUI.createPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM));

        // Create a tabbed pane for settings categories
        JTabbedPane settingsTabbedPane = ModernUI.createTabbedPane();

        // Create panels for each category
        Map<SettingCategory, JPanel> categoryPanels = new HashMap<>();
        for (SettingCategory category : SettingCategory.values()) {
            JPanel categoryPanel = ModernUI.createPanel(new GridLayout(0, 1, 0, ModernUI.PADDING_MEDIUM));
            categoryPanel.setBorder(BorderFactory.createEmptyBorder(ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM));
            categoryPanels.put(category, categoryPanel);
            settingsTabbedPane.addTab(category.getDisplayName(), categoryPanel);
        }

        // Add settings to their respective category panels, maintaining order within each category
        for (SettingCategory category : SettingCategory.values()) {
            JPanel categoryPanel = categoryPanels.get(category);
            if (categoryPanel != null) {
                // Get settings for this category in the order they were registered
                List<Setting<?>> categorySettings = SettingsManager.getSettingsByCategory(category);
                for (Setting<?> setting : categorySettings) {
                    JPanel settingPanel = createSettingPanel(setting);
                    categoryPanel.add(settingPanel);
                }
            }
        }

        // Add special controls
        JPanel scriptPanel = categoryPanels.get(SettingCategory.SCRIPT);
        if (scriptPanel != null) {
            // Add camera adjustment button to script panel
            JButton adjustCameraButton = ModernUI.createButton("Adjust Camera Now");
            adjustCameraButton.setPreferredSize(new Dimension(200, 30)); // Set a reasonable size
            adjustCameraButton.setMaximumSize(new Dimension(200, 30)); // Limit maximum size
            adjustCameraButton.addActionListener(e -> {
                // Create a new thread to adjust the camera
                new Thread(() -> {
                    try {
                        // Create a robot for keyboard input
                        Robot robot = new Robot();

                        // Press and hold the up arrow key to adjust camera angle
                        robot.keyPress(KeyEvent.VK_UP);

                        // Hold for 2 seconds
                        Thread.sleep(2000);

                        // Release the key
                        robot.keyRelease(KeyEvent.VK_UP);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }).start();
            });

            JPanel buttonPanel = ModernUI.createPanel(new BorderLayout());
            buttonPanel.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(ModernUI.PRIMARY_COLOR),
                    BorderFactory.createEmptyBorder(ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM)
            ));

            // Add a title label
            JLabel cameraControlLabel = ModernUI.createHeaderLabel("Camera Control");
            buttonPanel.add(cameraControlLabel, BorderLayout.NORTH);
            // Create a panel to center the button
            JPanel centerPanel = ModernUI.createPanel(new FlowLayout(FlowLayout.CENTER));
            centerPanel.add(adjustCameraButton);
            buttonPanel.add(centerPanel, BorderLayout.CENTER);
            scriptPanel.add(buttonPanel);
        }

        // Add the tabbed pane to the panel
        panel.add(settingsTabbedPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Creates a panel for a setting
     *
     * @param setting The setting
     * @return The panel
     */
    private JPanel createSettingPanel(Setting<?> setting) {
        JPanel panel = ModernUI.createPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(ModernUI.PRIMARY_COLOR),
                BorderFactory.createEmptyBorder(ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM)
        ));

        // Make the panel taller for integer settings (sliders)
        if (setting.getDefaultValue() instanceof Integer) {
            panel.setPreferredSize(new Dimension(panel.getPreferredSize().width, 120)); // Ensure enough height for the slider and labels
        }

        // Add setting title
        JLabel titleLabel = ModernUI.createHeaderLabel(setting.getDisplayName());
        panel.add(titleLabel, BorderLayout.NORTH);

        // Create the appropriate control based on the setting type
        if (setting.getDefaultValue() instanceof Boolean) {
            // Boolean setting - checkbox
            JCheckBox checkBox = new JCheckBox(setting.getDisplayName());
            checkBox.setFont(ModernUI.NORMAL_FONT);
            checkBox.setBackground(ModernUI.SECONDARY_COLOR);
            checkBox.setForeground(ModernUI.TEXT_COLOR);
            checkBox.setFocusPainted(false);
            checkBox.setSelected(SettingsManager.getBoolean(setting.getKey(), (Boolean) setting.getDefaultValue()));
            checkBox.addActionListener(e -> {
                boolean value = checkBox.isSelected();
                SettingsManager.setBoolean(setting.getKey(), value);

                // Apply special handling for certain settings
                if (setting == SettingsManager.MOUSE_CROSSHAIR_ENABLED) {
                    bot.ui.overlay.MouseCrosshair.setEnabled(value);
                } else if (setting == SettingsManager.AUTO_ADJUST_CAMERA) {
                    autoAdjustCamera = value;
                } else if (setting == SettingsManager.DEBUG_MODE_ENABLED) {
                    DebugModeManager.setDebugMode(value);
                } else if (setting == SettingsManager.ALWAYS_ON_TOP) {
                    Window window = SwingUtilities.getWindowAncestor(panel);
                    if (window instanceof JFrame) {
                        JFrame frame = (JFrame) window;
                        frame.setAlwaysOnTop(value);
                    }
                } else if (setting == SettingsManager.SHOW_ENTITY_IDS) {
                    DebugModeManager.setShowEntityIds(value);
                } else if (setting == SettingsManager.SHOW_TILE_LOCATIONS) {
                    DebugModeManager.setShowTileLocations(value);
                } else if (setting == SettingsManager.SHOW_WALLS_AND_DOORS) {
                    DebugModeManager.setShowWallsAndDoors(value);
                }
            });
            // Add tooltip with detailed description
            checkBox.setToolTipText(setting.getDescription());
            panel.add(checkBox, BorderLayout.CENTER);
        } else if (setting.getDefaultValue() instanceof Integer) {
            // Integer setting - slider
            int value = SettingsManager.getInt(setting.getKey(), (Integer) setting.getDefaultValue());
            // Use a more flexible layout for the slider panel
            JPanel sliderPanel = ModernUI.createPanel(new BorderLayout(ModernUI.PADDING_MEDIUM, ModernUI.PADDING_MEDIUM));
            sliderPanel.setBorder(BorderFactory.createEmptyBorder(ModernUI.PADDING_SMALL, ModernUI.PADDING_SMALL, ModernUI.PADDING_SMALL, ModernUI.PADDING_SMALL));

            // Create a value label with a fixed width to ensure it's visible
            JLabel valueLabel = ModernUI.createLabel(String.valueOf(value));
            valueLabel.setPreferredSize(new Dimension(50, valueLabel.getPreferredSize().height));
            valueLabel.setHorizontalAlignment(SwingConstants.RIGHT);
            sliderPanel.add(valueLabel, BorderLayout.EAST);

            // Determine slider range based on validation
            int min = 0;
            int max = 1000;
            if (setting == SettingsManager.SCRIPT_LOOP_DELAY) {
                min = 50;
                max = 500;
                scriptLoopDelay = value;
            } else if (setting == SettingsManager.FPS_LIMIT) {
                min = 1;
                max = 100;
            } else if (setting == SettingsManager.DRAW_DISTANCE) {
                min = 10;
                max = 50;
            } else if (setting == SettingsManager.HEAP_SIZE) {
                min = 256;
                max = 2048;
            }

            JSlider slider = ModernUI.createSlider(min, max, value);
            slider.addChangeListener(e -> {
                int newValue = slider.getValue();
                valueLabel.setText(String.valueOf(newValue));
                SettingsManager.setInt(setting.getKey(), newValue);

                // Apply special handling for certain settings
                if (setting == SettingsManager.SCRIPT_LOOP_DELAY) {
                    scriptLoopDelay = newValue;
                }
            });
            sliderPanel.add(slider, BorderLayout.CENTER);
            // Add tooltip with detailed description
            sliderPanel.setToolTipText(setting.getDescription());
            panel.add(sliderPanel, BorderLayout.CENTER);
        } else if (setting.getDefaultValue() instanceof String) {
            // String setting - special handling for entity type
            if (setting == SettingsManager.SELECTED_ENTITY_TYPE) {
                // Create a combo box for entity type selection
                JComboBox<DebugModeManager.EntityType> entityTypeComboBox = new JComboBox<>(DebugModeManager.EntityType.values());
                entityTypeComboBox.setFont(ModernUI.NORMAL_FONT);
                entityTypeComboBox.setBackground(ModernUI.PRIMARY_COLOR);
                entityTypeComboBox.setForeground(ModernUI.TEXT_COLOR);

                // Set the selected item based on the current setting
                String currentValue = SettingsManager.getString(setting.getKey(), (String) setting.getDefaultValue());
                try {
                    DebugModeManager.EntityType type = DebugModeManager.EntityType.valueOf(currentValue);
                    entityTypeComboBox.setSelectedItem(type);
                } catch (IllegalArgumentException ex) {
                    // If the value is invalid, select the first item
                    if (entityTypeComboBox.getItemCount() > 0) {
                        entityTypeComboBox.setSelectedIndex(0);
                    }
                }

                // Add action listener to update the setting when the selection changes
                entityTypeComboBox.addActionListener(e -> {
                    DebugModeManager.EntityType selectedType = (DebugModeManager.EntityType) entityTypeComboBox.getSelectedItem();
                    if (selectedType != null) {
                        SettingsManager.setString(setting.getKey(), selectedType.name());
                        DebugModeManager.getInstance().setSelectedEntityType(selectedType);
                    }
                });

                // Add tooltip with detailed description
                entityTypeComboBox.setToolTipText(setting.getDescription());
                panel.add(entityTypeComboBox, BorderLayout.CENTER);
            } else {
                // Regular string setting - text field
                String value = SettingsManager.getString(setting.getKey(), (String) setting.getDefaultValue());
                JTextField textField = ModernUI.createTextField();
                textField.setText(value);
                textField.addActionListener(e -> {
                    String newValue = textField.getText();
                    SettingsManager.setString(setting.getKey(), newValue);
                });
                // Add tooltip with detailed description
                textField.setToolTipText(setting.getDescription());
                panel.add(textField, BorderLayout.CENTER);
            }
        }

        return panel;
    }

    /**
     * Handles a setting change event
     *
     * @param key The setting key
     * @param oldValue The old value
     * @param newValue The new value
     */
    private void handleSettingChange(String key, Object oldValue, Object newValue) {
        // Update UI components based on the changed setting
        if (key.equals(SettingsManager.SCRIPT_LOOP_DELAY.getKey()) && newValue instanceof Integer) {
            scriptLoopDelay = (Integer) newValue;
        } else if (key.equals(SettingsManager.AUTO_ADJUST_CAMERA.getKey()) && newValue instanceof Boolean) {
            autoAdjustCamera = (Boolean) newValue;
        }
    }

    /**
     * Creates the status panel
     *
     * @return The status panel
     */
    private JPanel createStatusPanel() {
        JPanel panel = ModernUI.createPanel(new BorderLayout(ModernUI.PADDING_MEDIUM, 0));
        panel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createMatteBorder(1, 0, 0, 0, ModernUI.PRIMARY_COLOR),
                BorderFactory.createEmptyBorder(ModernUI.PADDING_SMALL, 0, 0, 0)));

        // Create the status label
        statusLabel = ModernUI.createHeaderLabel("Not running");
        panel.add(statusLabel, BorderLayout.WEST);

        // Create a panel for the progress bar to control its width
        JPanel progressPanel = ModernUI.createPanel(new BorderLayout());

        // Create the progress bar
        progressBar = ModernUI.createProgressBar();
        progressBar.setString("Not running");
        progressBar.setPreferredSize(new Dimension(200, progressBar.getPreferredSize().height));

        // Add progress bar to the right side of the progress panel
        progressPanel.add(progressBar, BorderLayout.EAST);

        // Add the progress panel to the main panel
        panel.add(progressPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Gets the content pane
     *
     * @return The content pane
     */
    public JPanel getContentPane() {
        return mainPanel;
    }

    /**
     * Shows the launcher
     */
    public void show() {
        // This method is no longer used directly
    }

    /**
     * Updates the UI
     */
    private void updateUI() {
        // Update the log
        updateLog();

        // Update the stats
        updateStats();

        // Update the status
        updateStatus();
    }

    // Entity table update method removed as requested

    /**
     * Updates the log
     */
    private void updateLog() {
        Logger.LogEntry[] entries = Logger.getLogEntries();

        // Only update the log if there are new entries
        if (entries.length > lastLogEntryCount) {
            // Save the current caret position and selection
            int caretPosition = logTextArea.getCaretPosition();
            int selectionStart = logTextArea.getSelectionStart();
            int selectionEnd = logTextArea.getSelectionEnd();
            boolean hasSelection = selectionStart != selectionEnd;
            boolean isAtEnd = caretPosition == logTextArea.getDocument().getLength();

            // Check if a script is running (for autoscroll)
            boolean scriptRunning = ScriptManager.getInstance().isScriptRunning();

            // Update the log text
            StringBuilder sb = new StringBuilder();
            for (Logger.LogEntry entry : entries) {
                sb.append(entry.toString()).append("\n");
            }
            logTextArea.setText(sb.toString());

            // Restore the caret position and selection
            if (hasSelection && !scriptRunning) {
                // Restore selection if there was one and no script is running
                logTextArea.setSelectionStart(selectionStart);
                logTextArea.setSelectionEnd(selectionEnd);
            } else if (isAtEnd || scriptRunning) {
                // Scroll to the end if the caret was at the end or a script is running
                logTextArea.setCaretPosition(logTextArea.getDocument().getLength());
            } else {
                // Otherwise, restore the caret position
                logTextArea.setCaretPosition(Math.min(caretPosition, logTextArea.getDocument().getLength()));
            }

            // Update the last entry count
            lastLogEntryCount = entries.length;
        }
    }

    /**
     * Updates the stats
     */
    private void updateStats() {
        // Clear the table
        while (statsTableModel.getRowCount() > 0) {
            statsTableModel.removeRow(0);
        }

        // Add the stats
        statsTableModel.addRow(new Object[] {"Running", ScriptManager.getInstance().isScriptRunning()});
        statsTableModel.addRow(new Object[] {"Script", ScriptManager.getInstance().getCurrentScript() != null ?
            ((Object)ScriptManager.getInstance().getCurrentScript()).getClass().getSimpleName() : "None"});
        statsTableModel.addRow(new Object[] {"Runtime", ScriptManager.getInstance().getFormattedScriptRuntime()});
        statsTableModel.addRow(new Object[] {"Loop Delay", scriptLoopDelay + " ms"});
        statsTableModel.addRow(new Object[] {"Auto-Adjust Camera", autoAdjustCamera});
        statsTableModel.addRow(new Object[] {"Game State", GameState.getGameState()});
        statsTableModel.addRow(new Object[] {"Logged In", GameState.isLoggedIn()});

        // Add player stats if logged in
        if (GameState.isLoggedIn()) {
            try {
                // Use bot.util.game.Player to safely access player information
                statsTableModel.addRow(new Object[] {"Player Name", Player.getName()});
                statsTableModel.addRow(new Object[] {"Combat Level", Player.getCombatLevel()});

                // Get player position (world coordinates)
                Tile playerPos = Player.getLocation();
                statsTableModel.addRow(new Object[] {"Position", "(" + playerPos.getGlobalX() + ", " + playerPos.getGlobalY() + ", " + playerPos.getPlane() + ")"});

                // Add run energy
                statsTableModel.addRow(new Object[] {"Run Energy", Player.getRunEnergy() + "%"});
            } catch (Exception e) {
                // Ignore exceptions when accessing player data
                System.out.println("Error accessing player data: " + e.getMessage());
            }
        }
    }

    /**
     * Updates the status
     */
    private void updateStatus() {
        try {
            boolean isScriptRunning = ScriptManager.getInstance().isScriptRunning();
            boolean isLoggedIn = false;
            try {
                isLoggedIn = GameState.isLoggedIn();
            } catch (Exception e) {
                // Ignore exceptions when checking login state
                // Error checking login state, but not critical enough to log in production
            }

            // Update status label and progress bar
            if (statusLabel != null) {
                if (isScriptRunning) {
                    // Script is running - check if paused
                    BotScript currentScript = ScriptManager.getInstance().getCurrentScript();
                    String scriptName = currentScript != null ? ((Object)currentScript).getClass().getSimpleName() : "Unknown";
                    boolean isScriptPaused = ScriptManager.getInstance().isScriptPaused();

                    if (isScriptPaused) {
                        statusLabel.setText("Paused: " + scriptName);
                        statusLabel.setForeground(new Color(255, 165, 0)); // Orange for paused

                        if (progressBar != null) {
                            progressBar.setString("Paused");
                            progressBar.setIndeterminate(false);
                            progressBar.setValue(50); // Show partial progress when paused
                        }
                    } else {
                        statusLabel.setText("Running: " + scriptName);
                        statusLabel.setForeground(new Color(0, 128, 0)); // Green for running

                        if (progressBar != null) {
                            progressBar.setString("Running");
                            progressBar.setIndeterminate(true);
                        }
                    }
                } else if (!isLoggedIn) {
                    // Not logged in
                    statusLabel.setText("Not logged in");
                    statusLabel.setForeground(Color.RED); // Red for not logged in

                    if (progressBar != null) {
                        progressBar.setString("Not logged in");
                        progressBar.setIndeterminate(false);
                        progressBar.setValue(0);
                    }
                } else {
                    // Logged in but no script running
                    statusLabel.setText("Ready");
                    statusLabel.setForeground(ModernUI.SUCCESS_COLOR); // Green for ready when logged in

                    if (progressBar != null) {
                        progressBar.setString("Ready");
                        progressBar.setIndeterminate(false);
                        progressBar.setValue(100);
                    }
                }
            }
        } catch (Exception e) {
            // Ignore exceptions when updating status
            // Error updating status - no need to log in production
        }
    }

    /**
     * Starts the update thread
     */
    private void startUpdateThread() {
        // Create and start the update thread
        updateThread = new Thread(() -> {
            while (true) {
                try {
                    // Update the UI
                    SwingUtilities.invokeLater(this::updateUI);

                    // Sleep for 1 second
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    // Thread was interrupted, exit
                    break;
                }
            }
        });
        updateThread.setDaemon(true);
        updateThread.start();
    }

    /**
     * Loads the scripts
     */
    private void loadScripts() {
        // Initialize the available scripts list if it's null
        if (availableScripts == null) {
            availableScripts = new ArrayList<>();
        } else {
            // Clear the list of available scripts
            availableScripts.clear();
        }

        try {
            // Load the built-in scripts
            loadBuiltInScripts();

            // Load the external scripts
            loadExternalScripts();

            // Note: updateScriptComboBox() is not called here because the script combo box
            // is part of legacy code that is no longer used. Scripts are now started
            // through the ScriptBrowserPanel instead.
        } catch (Exception e) {
            logger.error("Error loading scripts", e);
        }
    }

    /**
     * Loads the built-in scripts from bot.scripts package and all subdirectories
     */
    private void loadBuiltInScripts() {
        // Get all classes in the bot.scripts package (including subdirectories)
        Set<Class<?>> scriptClasses = findAllClassesInPackage("bot.scripts");

        // Log the number of classes found
        logger.info("Found " + scriptClasses.size() + " classes in bot.scripts package");

        // Debug: Log all found classes
        for (Class<?> clazz : scriptClasses) {
            logger.debug("Found class: " + clazz.getName() +
                        " (BotScript: " + BotScript.class.isAssignableFrom(clazz) +
                        ", Interface: " + clazz.isInterface() +
                        ", Abstract: " + java.lang.reflect.Modifier.isAbstract(clazz.getModifiers()) + ")");
        }

        // Add each script class that implements BotScript
        int validScriptCount = 0;
        for (Class<?> clazz : scriptClasses) {
            if (BotScript.class.isAssignableFrom(clazz) &&
                !clazz.isInterface() &&
                !java.lang.reflect.Modifier.isAbstract(clazz.getModifiers())) {

                // Format the script name for display
                String scriptName = formatScriptName(clazz.getSimpleName());

                // Add the script to the list
                availableScripts.add(new ScriptInfo(scriptName, clazz));
                validScriptCount++;

                logger.info("Loaded script: " + clazz.getSimpleName() + " as '" + scriptName + "'");
            }
        }

        logger.info("Loaded " + validScriptCount + " valid scripts out of " + scriptClasses.size() + " total classes");
    }

    /**
     * Loads the external scripts using the professional ExternalScriptManager
     */
    private void loadExternalScripts() {
        try {
            logger.info("Loading external scripts...");
            List<ScriptInfo> externalScripts = ExternalScriptManager.getInstance().loadAllScripts();

            if (!externalScripts.isEmpty()) {
                availableScripts.addAll(externalScripts);
                logger.info("Successfully loaded " + externalScripts.size() + " external scripts");
            } else {
                logger.info("No external scripts found in scripts directory");
            }
        } catch (Exception e) {
            logger.error("Failed to load external scripts", e);
        }
    }



    /**
     * Finds all classes in a package, including subdirectories
     *
     * @param packageName The package name
     * @return A set of classes in the package and its subdirectories
     */
    private Set<Class<?>> findAllClassesInPackage(String packageName) {
        Set<Class<?>> classes = new HashSet<>();

        try {
            // Get the class loader
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();

            // Get the package path
            String path = packageName.replace('.', '/');

            // Get all resources with the package path
            Enumeration<URL> resources = classLoader.getResources(path);

            // Iterate through all resources
            while (resources.hasMoreElements()) {
                URL resource = resources.nextElement();

                // Get the physical path of the resource
                String filePath = URLDecoder.decode(resource.getFile(), "UTF-8");

                // Check if the resource is a directory
                File directory = new File(filePath);
                if (directory.exists()) {
                    // Recursively scan the directory
                    scanDirectoryForClasses(directory, packageName, classes);
                }
            }
        } catch (IOException e) {
            logger.error("Error scanning package: " + packageName, e);
        }

        return classes;
    }

    /**
     * Recursively scans a directory for class files
     *
     * @param directory The directory to scan
     * @param packageName The current package name
     * @param classes The set to add found classes to
     */
    private void scanDirectoryForClasses(File directory, String packageName, Set<Class<?>> classes) {
        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                // Recursively scan subdirectories
                String subPackageName = packageName + "." + file.getName();
                scanDirectoryForClasses(file, subPackageName, classes);
            } else if (file.getName().endsWith(".class")) {
                // Skip inner classes (contain $)
                if (file.getName().contains("$")) {
                    logger.debug("Skipping inner class: " + file.getName());
                    continue;
                }

                // Get the class name
                String className = packageName + "." + file.getName().substring(0, file.getName().length() - 6);

                try {
                    // Load the class
                    Class<?> clazz = Class.forName(className);
                    classes.add(clazz);
                    logger.debug("Found class: " + className);
                } catch (ClassNotFoundException e) {
                    logger.warning("Could not load class: " + className + " - " + e.getMessage());
                } catch (NoClassDefFoundError e) {
                    logger.warning("Class definition error for: " + className + " - " + e.getMessage());
                }
            }
        }
    }

    /**
     * Formats a script name for display
     *
     * @param scriptName The script name
     * @return The formatted script name
     */
    private String formatScriptName(String scriptName) {
        // Remove "Script" suffix if present
        if (scriptName.endsWith("Script")) {
            scriptName = scriptName.substring(0, scriptName.length() - 6);
        }

        // Add spaces before capital letters
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < scriptName.length(); i++) {
            char c = scriptName.charAt(i);
            if (i > 0 && Character.isUpperCase(c)) {
                sb.append(' ');
            }
            sb.append(c);
        }

        return sb.toString();
    }



    /**
     * Script info class
     */
    public static class ScriptInfo {
        private final String name;
        private final Class<?> scriptClass;

        /**
         * Creates a new ScriptInfo
         *
         * @param name The name of the script
         * @param scriptClass The script class
         */
        public ScriptInfo(String name, Class<?> scriptClass) {
            this.name = name;
            this.scriptClass = scriptClass;
        }

        /**
         * Gets the name of the script
         *
         * @return The name of the script
         */
        public String getName() {
            return name;
        }

        /**
         * Gets the script class
         *
         * @return The script class
         */
        public Class<?> getScriptClass() {
            return scriptClass;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    /**
     * Checks if this BotUI window has focus
     * @return true if the BotUI window has focus
     */
    public boolean hasFocus() {
        Window window = SwingUtilities.getWindowAncestor(mainPanel);
        return window != null && window.isFocused();
    }

    /**
     * Gets the available scripts
     *
     * @return The list of available scripts
     */
    public List<ScriptInfo> getAvailableScripts() {
        return availableScripts;
    }

    /**
     * Handles script loading events from the ExternalScriptManager
     */
    @Override
    public void onScriptLoadEvent(ScriptLoadEvent event) {
        SwingUtilities.invokeLater(() -> {
            switch (event.getType()) {
                case SUCCESS:
                    logger.info("External script loaded: " + event.getScriptFileName() +
                               " (" + event.getScriptCount() + " scripts)");
                    break;

                case FAILURE:
                    logger.warning("Failed to load external script: " + event.getScriptFileName() +
                                  " - " + event.getMessage());
                    break;

                case UNLOADED:
                    logger.info("External script unloaded: " + event.getScriptFileName());
                    break;

                case RELOADED:
                    logger.info("External script reloaded: " + event.getScriptFileName() +
                               " (" + event.getScriptCount() + " scripts)");
                    break;
            }

            // Refresh the script browser if it exists
            // This ensures the UI stays up-to-date with script changes
            if (tabbedPane != null) {
                for (int i = 0; i < tabbedPane.getTabCount(); i++) {
                    if (tabbedPane.getComponentAt(i) instanceof ScriptBrowserPanel) {
                        ScriptBrowserPanel browserPanel = (ScriptBrowserPanel) tabbedPane.getComponentAt(i);
                        // Trigger a refresh of the script list
                        browserPanel.updateScripts(availableScripts);
                        break;
                    }
                }
            }
        });
    }
}
