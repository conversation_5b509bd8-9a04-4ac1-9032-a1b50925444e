package bot.ui.paint.metrics;

import bot.ui.paint.PaintMetric;
import bot.ui.paint.PaintMetric.MetricCategory;

import java.text.DecimalFormat;
import java.util.concurrent.TimeUnit;

/**
 * Utility class for creating and formatting common paint metrics.
 * Provides standardized formatting for numbers, time, rates, etc.
 */
public class MetricsHelper {
    
    private static final DecimalFormat NUMBER_FORMAT = new DecimalFormat("#,###");
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#,###.##");
    private static final DecimalFormat RATE_FORMAT = new DecimalFormat("#,###.#");
    
    /**
     * Formats a number with thousands separators.
     */
    public static String formatNumber(long number) {
        return NUMBER_FORMAT.format(number);
    }
    
    /**
     * Formats a number with thousands separators.
     */
    public static String formatNumber(int number) {
        return NUMBER_FORMAT.format(number);
    }
    
    /**
     * Formats a decimal number with thousands separators.
     */
    public static String formatDecimal(double number) {
        return DECIMAL_FORMAT.format(number);
    }
    
    /**
     * Formats a rate (per hour) value.
     */
    public static String formatRate(double rate) {
        return RATE_FORMAT.format(rate) + "/hr";
    }
    
    /**
     * Formats elapsed time in a readable format.
     */
    public static String formatTime(long milliseconds) {
        if (milliseconds < 0) {
            return "00:00:00";
        }
        
        long hours = TimeUnit.MILLISECONDS.toHours(milliseconds);
        long minutes = TimeUnit.MILLISECONDS.toMinutes(milliseconds) % 60;
        long seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds) % 60;
        
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }
    
    /**
     * Calculates rate per hour based on count and elapsed time.
     */
    public static double calculateRate(int count, long elapsedMillis) {
        if (elapsedMillis <= 0) {
            return 0.0;
        }
        double hoursElapsed = elapsedMillis / (1000.0 * 60.0 * 60.0);
        return count / hoursElapsed;
    }
    
    /**
     * Calculates rate per hour based on count and elapsed time.
     */
    public static double calculateRate(long count, long elapsedMillis) {
        if (elapsedMillis <= 0) {
            return 0.0;
        }
        double hoursElapsed = elapsedMillis / (1000.0 * 60.0 * 60.0);
        return count / hoursElapsed;
    }
    
    /**
     * Creates a runtime metric.
     */
    public static PaintMetric createRuntimeMetric(long startTime) {
        long elapsed = System.currentTimeMillis() - startTime;
        return new PaintMetric("Runtime", formatTime(elapsed), MetricCategory.RUNTIME);
    }

    /**
     * Creates an XP gained metric.
     */
    public static PaintMetric createXPMetric(int xpGained) {
        return new PaintMetric("XP Gained", formatNumber(xpGained), MetricCategory.EXPERIENCE);
    }

    /**
     * Creates an XP per hour metric.
     */
    public static PaintMetric createXPRateMetric(double xpPerHour) {
        return new PaintMetric("XP/Hour", formatRate(xpPerHour), MetricCategory.EXPERIENCE);
    }

    /**
     * Creates an item count metric.
     */
    public static PaintMetric createItemMetric(String itemName, int count) {
        return new PaintMetric(itemName, formatNumber(count), MetricCategory.ITEMS);
    }

    /**
     * Creates an item rate metric.
     */
    public static PaintMetric createItemRateMetric(String itemName, double rate) {
        return new PaintMetric(itemName + "/Hour", formatRate(rate), MetricCategory.ITEMS);
    }
    
    /**
     * Creates a banking trips metric.
     */
    public static PaintMetric createBankingTripsMetric(int trips) {
        return new PaintMetric("Banking Trips", formatNumber(trips), MetricCategory.BANKING, 30);
    }
    
    /**
     * Creates a banking trips rate metric.
     */
    public static PaintMetric createBankingRateMetric(double rate) {
        return new PaintMetric("Trips/Hour", formatRate(rate), MetricCategory.BANKING, 31);
    }
    
    /**
     * Creates a general count metric.
     */
    public static PaintMetric createCountMetric(String label, int count) {
        return new PaintMetric(label, formatNumber(count), MetricCategory.GENERAL, 50);
    }
    
    /**
     * Creates a general rate metric.
     */
    public static PaintMetric createRateMetric(String label, double rate) {
        return new PaintMetric(label + "/Hour", formatRate(rate), MetricCategory.GENERAL, 51);
    }
    
    /**
     * Creates a percentage metric.
     */
    public static PaintMetric createPercentageMetric(String label, double percentage) {
        return new PaintMetric(label, formatDecimal(percentage) + "%", MetricCategory.GENERAL, 60);
    }
    
    /**
     * Creates a status metric.
     */
    public static PaintMetric createStatusMetric(String status) {
        return new PaintMetric("Status", status, MetricCategory.GENERAL, 5);
    }
}
