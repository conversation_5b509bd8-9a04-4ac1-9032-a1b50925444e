package bot.ui.paint;

/**
 * Represents a single paint metric with a label and formatted value.
 * Used by the paint system to display script statistics in a consistent format.
 */
public class PaintMetric {
    private final String label;
    private final String value;
    private final MetricCategory category;
    
    /**
     * Creates a new paint metric.
     * 
     * @param label The display label for this metric
     * @param value The formatted value to display
     * @param category The category this metric belongs to
     * @param priority The display priority (lower numbers display first)
     */
    public PaintMetric(String label, String value, MetricCategory category, int priority) {
        this.label = label;
        this.value = value;
        this.category = category;
        this.priority = priority;
    }
    
    /**
     * Creates a new paint metric with default category and priority.
     * 
     * @param label The display label for this metric
     * @param value The formatted value to display
     */
    public PaintMetric(String label, String value) {
        this(label, value, MetricCategory.GENERAL, 100);
    }
    
    /**
     * Creates a new paint metric with specified category.
     * 
     * @param label The display label for this metric
     * @param value The formatted value to display
     * @param category The category this metric belongs to
     */
    public PaintMetric(String label, String value, MetricCategory category) {
        this(label, value, category, 100);
    }
    
    /**
     * Gets the display label.
     */
    public String getLabel() {
        return label;
    }
    
    /**
     * Gets the formatted value.
     */
    public String getValue() {
        return value;
    }
    
    /**
     * Gets the metric category.
     */
    public MetricCategory getCategory() {
        return category;
    }
    
    /**
     * Gets the display priority.
     */
    public int getPriority() {
        return priority;
    }
    
    /**
     * Enum for metric categories to help organize the display.
     */
    public enum MetricCategory {
        RUNTIME("Runtime"),
        EXPERIENCE("Experience"),
        ITEMS("Items"),
        BANKING("Banking"),
        COMBAT("Combat"),
        GENERAL("General");
        
        private final String displayName;
        
        MetricCategory(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    @Override
    public String toString() {
        return label + ": " + value;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PaintMetric that = (PaintMetric) obj;
        return priority == that.priority &&
               label.equals(that.label) &&
               value.equals(that.value) &&
               category == that.category;
    }
    
    @Override
    public int hashCode() {
        int result = label.hashCode();
        result = 31 * result + value.hashCode();
        result = 31 * result + category.hashCode();
        result = 31 * result + priority;
        return result;
    }
}
