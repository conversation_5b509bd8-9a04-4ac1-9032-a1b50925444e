package bot.ui.paint;

import java.util.Map;

/**
 * Interface for scripts that want to provide paint metrics to the dashboard.
 * Scripts implementing this interface will automatically have their metrics
 * displayed in real-time on the paint dashboard.
 */
public interface PaintProvider {
    
    /**
     * Gets the current paint metrics for this script.
     * This method is called regularly by the PaintManager to update the dashboard.
     * 
     * @return A map of metric keys to PaintMetric objects, or null if no metrics available
     */
    Map<String, PaintMetric> getPaintMetrics();
}
