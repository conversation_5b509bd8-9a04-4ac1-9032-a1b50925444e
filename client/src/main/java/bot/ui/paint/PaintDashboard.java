package bot.ui.paint;

import bot.ui.ModernUI;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * Clean dashboard for script performance monitoring.
 * Scripts can directly update the dashboard with simple data.
 */
public class PaintDashboard extends JPanel implements ActionListener {
    private static final Logger logger = LoggerFactory.getLogger(PaintDashboard.class);

    private JLabel scriptNameLabel;
    private JLabel statusLabel;
    private JPanel paintDataPanel;
    private JScrollPane scrollPane;
    private Timer updateTimer;
    private String currentScriptName = "No script running";
    private Object currentScript = null;
    private boolean isActive;
    private JButton pauseButton;
    private JButton stopButton;
    private JPanel controlPanel;
    
    public PaintDashboard() {
        this.isActive = false;
        initializeComponents();
        showWelcomeMessage();
        startUpdateTimer();
        logger.info("Clean paint dashboard initialized");
    }

    private void initializeComponents() {
        setLayout(new BorderLayout());
        setBackground(ModernUI.PRIMARY_COLOR);

        // Clean header with script info
        add(createHeaderPanel(), BorderLayout.NORTH);

        // Modular, scrollable content area
        paintDataPanel = new JPanel();
        paintDataPanel.setLayout(new BoxLayout(paintDataPanel, BoxLayout.Y_AXIS));
        paintDataPanel.setBackground(ModernUI.PRIMARY_COLOR);
        paintDataPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));

        // Professional scrollpane with smooth scrolling
        scrollPane = new JScrollPane(paintDataPanel);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.getVerticalScrollBar().setUnitIncrement(20);
        scrollPane.getVerticalScrollBar().setBlockIncrement(60);
        scrollPane.setBackground(ModernUI.PRIMARY_COLOR);
        scrollPane.getViewport().setBackground(ModernUI.PRIMARY_COLOR);

        add(scrollPane, BorderLayout.CENTER);
    }
    
    private JPanel createHeaderPanel() {
        JPanel headerPanel = ModernUI.createPanel(new BorderLayout());
        headerPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createMatteBorder(0, 0, 1, 0, ModernUI.BORDER_COLOR),
            BorderFactory.createEmptyBorder(20, 25, 20, 25)
        ));

        // Title section
        JPanel titlePanel = new JPanel();
        titlePanel.setLayout(new BoxLayout(titlePanel, BoxLayout.Y_AXIS));
        titlePanel.setOpaque(false);

        JLabel titleLabel = ModernUI.createLabel("Performance Dashboard");
        titleLabel.setFont(new Font(titleLabel.getFont().getName(), Font.BOLD, 18));
        titleLabel.setForeground(ModernUI.TEXT_COLOR);
        titleLabel.setAlignmentX(Component.LEFT_ALIGNMENT);

        JLabel subtitleLabel = ModernUI.createLabel("Real-time script monitoring");
        subtitleLabel.setFont(new Font(subtitleLabel.getFont().getName(), Font.PLAIN, 12));
        subtitleLabel.setForeground(ModernUI.TEXT_SECONDARY_COLOR);
        subtitleLabel.setAlignmentX(Component.LEFT_ALIGNMENT);

        titlePanel.add(titleLabel);
        titlePanel.add(Box.createVerticalStrut(2));
        titlePanel.add(subtitleLabel);

        // Status section
        JPanel statusPanel = new JPanel();
        statusPanel.setLayout(new BoxLayout(statusPanel, BoxLayout.Y_AXIS));
        statusPanel.setOpaque(false);
        statusPanel.setAlignmentY(Component.CENTER_ALIGNMENT);

        scriptNameLabel = ModernUI.createLabel("No script running");
        scriptNameLabel.setFont(new Font(scriptNameLabel.getFont().getName(), Font.BOLD, 14));
        scriptNameLabel.setForeground(ModernUI.TEXT_COLOR);
        scriptNameLabel.setHorizontalAlignment(SwingConstants.RIGHT);
        scriptNameLabel.setAlignmentX(Component.RIGHT_ALIGNMENT);

        statusLabel = ModernUI.createLabel("Idle");
        statusLabel.setFont(new Font(statusLabel.getFont().getName(), Font.PLAIN, 12));
        statusLabel.setForeground(ModernUI.TEXT_SECONDARY_COLOR);
        statusLabel.setHorizontalAlignment(SwingConstants.RIGHT);
        statusLabel.setAlignmentX(Component.RIGHT_ALIGNMENT);

        statusPanel.add(scriptNameLabel);
        statusPanel.add(Box.createVerticalStrut(2));
        statusPanel.add(statusLabel);

        // Control buttons panel
        controlPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0));
        controlPanel.setOpaque(false);

        pauseButton = ModernUI.createButton("Pause");
        pauseButton.setFont(new Font(pauseButton.getFont().getName(), Font.PLAIN, 11));
        pauseButton.addActionListener(e -> {
            if (bot.core.ScriptManager.getInstance().isScriptPaused()) {
                bot.core.ScriptManager.getInstance().resumeScript();
                updatePauseResumeStatus();
            } else {
                bot.core.ScriptManager.getInstance().pauseScript();
                updatePauseResumeStatus();
            }
        });

        stopButton = ModernUI.createButton("Stop");
        stopButton.setFont(new Font(stopButton.getFont().getName(), Font.PLAIN, 11));
        stopButton.addActionListener(e -> {
            bot.core.ScriptManager.getInstance().stopScript();
        });

        controlPanel.add(pauseButton);
        controlPanel.add(stopButton);

        // Add all panels to header
        headerPanel.add(titlePanel, BorderLayout.WEST);
        headerPanel.add(statusPanel, BorderLayout.CENTER);
        headerPanel.add(controlPanel, BorderLayout.EAST);
        return headerPanel;
    }

    private void showWelcomeMessage() {
        paintDataPanel.removeAll();

        JPanel welcomePanel = ModernUI.createPanel(new BorderLayout());
        welcomePanel.setBorder(BorderFactory.createEmptyBorder(60, 40, 60, 40));

        // Create main welcome content
        JPanel contentPanel = new JPanel();
        contentPanel.setLayout(new BoxLayout(contentPanel, BoxLayout.Y_AXIS));
        contentPanel.setOpaque(false);
        contentPanel.setAlignmentX(Component.CENTER_ALIGNMENT);

        // Title
        JLabel titleLabel = ModernUI.createLabel("Script Performance Dashboard");
        titleLabel.setFont(new Font(titleLabel.getFont().getName(), Font.BOLD, 24));
        titleLabel.setForeground(ModernUI.TEXT_COLOR);
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);

        // Subtitle
        JLabel subtitleLabel = ModernUI.createLabel("Real-time monitoring for bot scripts");
        subtitleLabel.setFont(new Font(subtitleLabel.getFont().getName(), Font.PLAIN, 14));
        subtitleLabel.setForeground(ModernUI.TEXT_SECONDARY_COLOR);
        subtitleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        subtitleLabel.setHorizontalAlignment(SwingConstants.CENTER);

        // Instructions
        JLabel instructionsLabel = ModernUI.createLabel("Start a script to view live performance metrics");
        instructionsLabel.setFont(new Font(instructionsLabel.getFont().getName(), Font.PLAIN, 12));
        instructionsLabel.setForeground(ModernUI.TEXT_SECONDARY_COLOR);
        instructionsLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        instructionsLabel.setHorizontalAlignment(SwingConstants.CENTER);

        // Add components with spacing
        contentPanel.add(titleLabel);
        contentPanel.add(Box.createVerticalStrut(15));
        contentPanel.add(subtitleLabel);
        contentPanel.add(Box.createVerticalStrut(10));
        contentPanel.add(instructionsLabel);

        welcomePanel.add(contentPanel, BorderLayout.CENTER);
        paintDataPanel.add(welcomePanel);

        revalidate();
        repaint();
    }
    
    /**
     * Sets the current script name for display.
     */
    public void setScriptName(String scriptName) {
        this.currentScriptName = scriptName;
        if (scriptName != null && !scriptName.equals("No script running")) {
            scriptNameLabel.setText(scriptName);
            statusLabel.setText("Running");
            isActive = true;
            // Show control buttons when script is running
            controlPanel.setVisible(true);
            updatePauseResumeStatus();
            logger.info("Script started: " + scriptName);
        } else {
            scriptNameLabel.setText("No script running");
            statusLabel.setText("Idle");
            isActive = false;
            // Hide control buttons when no script is running
            controlPanel.setVisible(false);
            showWelcomeMessage();
            logger.info("Script stopped");
        }
    }
    

    
    /**
     * Simple method to add a metric to the dashboard.
     */
    public void addMetric(String label, String value) {
        if (!isActive) return;

        // Create a simple metric display
        JPanel metricPanel = new JPanel(new BorderLayout());
        metricPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(ModernUI.BORDER_COLOR, 1, true),
            BorderFactory.createEmptyBorder(16, 20, 16, 20)
        ));
        metricPanel.setBackground(new Color(50, 53, 55));
        metricPanel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 70));

        JLabel nameLabel = ModernUI.createLabel(label);
        nameLabel.setFont(new Font(nameLabel.getFont().getName(), Font.PLAIN, 14));
        nameLabel.setForeground(ModernUI.TEXT_SECONDARY_COLOR);

        JLabel valueLabel = ModernUI.createLabel(value);
        valueLabel.setFont(new Font(valueLabel.getFont().getName(), Font.BOLD, 16));
        valueLabel.setForeground(ModernUI.TEXT_COLOR);
        valueLabel.setHorizontalAlignment(SwingConstants.RIGHT);

        metricPanel.add(nameLabel, BorderLayout.WEST);
        metricPanel.add(valueLabel, BorderLayout.EAST);

        paintDataPanel.add(metricPanel);
        paintDataPanel.add(Box.createVerticalStrut(12));

        paintDataPanel.revalidate();
        paintDataPanel.repaint();
    }

    /**
     * Updates the dashboard with a map of metrics.
     * This is the main method used by the PaintManager for real-time updates.
     */
    public void updateMetrics(java.util.Map<String, PaintMetric> metrics) {
        if (!isActive || metrics == null || metrics.isEmpty()) {
            return;
        }

        // Clear existing metrics
        clearMetrics();

        // Sort metrics by category and priority
        java.util.List<java.util.Map.Entry<String, PaintMetric>> sortedMetrics =
            new java.util.ArrayList<>(metrics.entrySet());
        sortedMetrics.sort((a, b) -> {
            PaintMetric metricA = a.getValue();
            PaintMetric metricB = b.getValue();

            // Sort by category only
            return metricA.getCategory().compareTo(metricB.getCategory());
        });

        // Group metrics by category
        java.util.Map<PaintMetric.MetricCategory, java.util.List<PaintMetric>> groupedMetrics =
            new java.util.LinkedHashMap<>();

        for (java.util.Map.Entry<String, PaintMetric> entry : sortedMetrics) {
            PaintMetric metric = entry.getValue();
            groupedMetrics.computeIfAbsent(metric.getCategory(), k -> new java.util.ArrayList<>()).add(metric);
        }

        // Display metrics grouped by category
        boolean firstCategory = true;
        for (java.util.Map.Entry<PaintMetric.MetricCategory, java.util.List<PaintMetric>> categoryEntry : groupedMetrics.entrySet()) {
            PaintMetric.MetricCategory category = categoryEntry.getKey();
            java.util.List<PaintMetric> categoryMetrics = categoryEntry.getValue();

            // Add category separator (except for first category)
            if (!firstCategory) {
                paintDataPanel.add(Box.createVerticalStrut(20));
            }
            firstCategory = false;

            // Add category header if there are multiple categories
            if (groupedMetrics.size() > 1) {
                addCategoryHeader(category.getDisplayName());
            }

            // Add metrics for this category
            for (PaintMetric metric : categoryMetrics) {
                addMetricPanel(metric);
            }
        }

        paintDataPanel.revalidate();
        paintDataPanel.repaint();
    }

    /**
     * Clear all metrics from the dashboard.
     */
    public void clearMetrics() {
        paintDataPanel.removeAll();
        paintDataPanel.revalidate();
        paintDataPanel.repaint();
    }

    /**
     * Adds a category header to the dashboard.
     */
    private void addCategoryHeader(String categoryName) {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(ModernUI.PRIMARY_COLOR);
        headerPanel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 40));

        JLabel headerLabel = ModernUI.createLabel(categoryName);
        headerLabel.setFont(new Font(headerLabel.getFont().getName(), Font.BOLD, 14));
        headerLabel.setForeground(ModernUI.ACCENT_COLOR);
        headerLabel.setBorder(BorderFactory.createEmptyBorder(10, 5, 5, 5));

        headerPanel.add(headerLabel, BorderLayout.WEST);

        paintDataPanel.add(headerPanel);
        paintDataPanel.add(Box.createVerticalStrut(5));
    }

    /**
     * Adds a metric panel to the dashboard.
     */
    private void addMetricPanel(PaintMetric metric) {
        JPanel metricPanel = new JPanel(new BorderLayout());
        metricPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(ModernUI.BORDER_COLOR, 1, true),
            BorderFactory.createEmptyBorder(16, 20, 16, 20)
        ));
        metricPanel.setBackground(new Color(50, 53, 55));
        metricPanel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 70));

        JLabel nameLabel = ModernUI.createLabel(metric.getLabel());
        nameLabel.setFont(new Font(nameLabel.getFont().getName(), Font.PLAIN, 14));
        nameLabel.setForeground(ModernUI.TEXT_SECONDARY_COLOR);

        JLabel valueLabel = ModernUI.createLabel(metric.getValue());
        valueLabel.setFont(new Font(valueLabel.getFont().getName(), Font.BOLD, 16));
        valueLabel.setForeground(ModernUI.TEXT_COLOR);
        valueLabel.setHorizontalAlignment(SwingConstants.RIGHT);

        metricPanel.add(nameLabel, BorderLayout.WEST);
        metricPanel.add(valueLabel, BorderLayout.EAST);

        paintDataPanel.add(metricPanel);
        paintDataPanel.add(Box.createVerticalStrut(12));
    }





    private void startUpdateTimer() {
        if (updateTimer != null) {
            updateTimer.stop();
        }
        updateTimer = new Timer(100, this); // Faster updates for responsiveness
        updateTimer.setRepeats(true);
        updateTimer.start();
        logger.debug("Paint dashboard update timer started (100ms interval)");
    }

    public void stopUpdateTimer() {
        if (updateTimer != null) {
            updateTimer.stop();
            logger.debug("Paint dashboard update timer stopped");
        }
    }

    @Override
    public void actionPerformed(ActionEvent e) {
        // Timer for dashboard updates - scripts can update directly
        // Also update pause/resume status periodically
        updatePauseResumeStatus();
    }

    /**
     * Updates the pause/resume button text and status label based on script state.
     */
    private void updatePauseResumeStatus() {
        if (!isActive) {
            return;
        }

        boolean isScriptPaused = bot.core.ScriptManager.getInstance().isScriptPaused();
        boolean isScriptRunning = bot.core.ScriptManager.getInstance().isScriptRunning();

        if (!isScriptRunning) {
            // Script stopped, this will be handled by setScriptName
            return;
        }

        if (isScriptPaused) {
            pauseButton.setText("Resume");
            statusLabel.setText("Paused");
        } else {
            pauseButton.setText("Pause");
            statusLabel.setText("Running");
        }
    }
}
