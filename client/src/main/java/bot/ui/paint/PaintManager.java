package bot.ui.paint;

import bot.util.event.EventBus;
import bot.util.event.EventSubscriber;
import bot.util.event.events.ScriptStartEvent;
import bot.util.event.events.ScriptStopEvent;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;

import javax.swing.Timer;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Manages the integration between scripts and the paint dashboard.
 * Automatically detects when scripts start/stop and updates the dashboard.
 * Provides real-time metric collection and display system.
 */
public class PaintManager implements ActionListener {
    private static final Logger logger = LoggerFactory.getLogger(PaintManager.class);
    private static PaintManager instance;

    private PaintDashboard dashboard;
    private PaintProvider currentScript;
    private Timer updateTimer;
    private final Map<String, PaintMetric> currentMetrics = new ConcurrentHashMap<>();

    /**
     * Gets the singleton instance of PaintManager.
     */
    public static synchronized PaintManager getInstance() {
        if (instance == null) {
            instance = new PaintManager();
        }
        return instance;
    }

    /**
     * Private constructor for singleton pattern.
     */
    private PaintManager() {
        registerEventListeners();
        startUpdateTimer();
        logger.info("PaintManager initialized");
    }

    /**
     * Sets the dashboard that will display paint data.
     */
    public void setDashboard(PaintDashboard dashboard) {
        this.dashboard = dashboard;
        logger.info("Dashboard connected to PaintManager");
    }

    /**
     * Gets the dashboard instance.
     */
    public PaintDashboard getDashboard() {
        return dashboard;
    }

    /**
     * Registers a script as a paint provider.
     */
    public void registerScript(PaintProvider script) {
        this.currentScript = script;
        if (dashboard != null) {
            dashboard.setScriptName(script.getClass().getSimpleName());
            dashboard.clearMetrics();
        }
        logger.info("Registered paint provider: " + script.getClass().getSimpleName());
    }

    /**
     * Unregisters the current script.
     */
    public void unregisterScript() {
        this.currentScript = null;
        this.currentMetrics.clear();
        if (dashboard != null) {
            dashboard.setScriptName("No script running");
            dashboard.clearMetrics();
        }
        logger.info("Unregistered paint provider");
    }

    /**
     * Updates a specific metric.
     */
    public void updateMetric(String key, PaintMetric metric) {
        currentMetrics.put(key, metric);
    }

    /**
     * Gets the current metrics map.
     */
    public Map<String, PaintMetric> getCurrentMetrics() {
        return new ConcurrentHashMap<>(currentMetrics);
    }

    /**
     * Starts the update timer for real-time updates.
     */
    private void startUpdateTimer() {
        if (updateTimer != null) {
            updateTimer.stop();
        }
        updateTimer = new Timer(1000, this); // Update every second
        updateTimer.setRepeats(true);
        updateTimer.start();
        logger.debug("Paint update timer started (1000ms interval)");
    }

    /**
     * Stops the update timer.
     */
    public void stopUpdateTimer() {
        if (updateTimer != null) {
            updateTimer.stop();
            logger.debug("Paint update timer stopped");
        }
    }

    @Override
    public void actionPerformed(ActionEvent e) {
        // Update metrics from current script
        if (currentScript != null && dashboard != null) {
            try {
                Map<String, PaintMetric> metrics = currentScript.getPaintMetrics();
                if (metrics != null) {
                    // Update our internal metrics
                    currentMetrics.putAll(metrics);

                    // Update dashboard
                    dashboard.updateMetrics(metrics);
                }
            } catch (Exception ex) {
                logger.error("Error updating paint metrics", ex);
            }
        }
    }

    /**
     * Registers event listeners for script start/stop events.
     */
    private void registerEventListeners() {
        // Listen for script start events
        EventBus.getInstance().register(ScriptStartEvent.class, new EventSubscriber<ScriptStartEvent>() {
            @Override
            public void onEvent(ScriptStartEvent event) {
                Object script = event.getScript();
                String scriptName = script.getClass().getSimpleName();
                logger.info("Script started: " + scriptName);

                // Register script if it implements PaintProvider
                if (script instanceof PaintProvider) {
                    registerScript((PaintProvider) script);
                } else {
                    // For scripts that don't implement PaintProvider, just update the name
                    if (dashboard != null) {
                        dashboard.setScriptName(scriptName);
                        dashboard.clearMetrics();
                    }
                }
            }
        });

        // Listen for script stop events
        EventBus.getInstance().register(ScriptStopEvent.class, new EventSubscriber<ScriptStopEvent>() {
            @Override
            public void onEvent(ScriptStopEvent event) {
                logger.info("Script stopped");
                unregisterScript();
            }
        });
    }
}
