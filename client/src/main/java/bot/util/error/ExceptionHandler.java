package bot.util.error;

import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.util.event.EventBus;
import bot.util.event.events.ScriptErrorEvent;
import bot.api.script.BotScript;

import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

/**
 * Centralized exception handling utility for the bot framework.
 * Provides standardized methods for handling different types of exceptions.
 */
public class ExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger("ExceptionHandler");

    /**
     * Handles an exception with standardized logging and optional callback.
     *
     * @param exception The exception to handle
     * @param errorMessage The error message to log
     * @param errorCallback Optional callback to execute after handling the exception
     */
    public static void handleException(Exception exception, String errorMessage, Consumer<Exception> errorCallback) {
        if (exception instanceof InterruptedException) {
            // Special handling for InterruptedException
            handleInterruptedException((InterruptedException) exception);
        } else if (exception instanceof TimeoutException) {
            // Special handling for TimeoutException
            handleTimeoutException((TimeoutException) exception, errorMessage);
        } else {
            // General exception handling
            logger.error(errorMessage + ": " + exception.getMessage(), exception);
        }

        // Execute callback if provided
        if (errorCallback != null) {
            errorCallback.accept(exception);
        }
    }

    /**
     * Handles a script exception with standardized logging and event publishing.
     *
     * @param script The script that encountered the exception
     * @param exception The exception to handle
     * @param errorMessage The error message to log
     */
    public static void handleScriptException(BotScript script, Exception exception, String errorMessage) {
        // Log the exception
        logger.error(errorMessage + ": " + exception.getMessage(), exception);

        // Publish script error event
        try {
            EventBus.getInstance().publish(new ScriptErrorEvent(script, exception, errorMessage));
        } catch (Exception eventError) {
            logger.error("Error publishing script error event", eventError);
        }
    }

    /**
     * Handles an InterruptedException with standardized logging.
     * InterruptedException should generally be propagated to allow proper script pausing/stopping.
     *
     * @param exception The InterruptedException to handle
     */
    public static void handleInterruptedException(InterruptedException exception) {
        logger.info("Thread interrupted, propagating interruption");
        // Restore the interrupted status
        Thread.currentThread().interrupt();
    }

    /**
     * Handles a TimeoutException with standardized logging.
     *
     * @param exception The TimeoutException to handle
     * @param errorMessage The error message to log
     */
    public static void handleTimeoutException(TimeoutException exception, String errorMessage) {
        logger.warning(errorMessage + ": " + exception.getMessage());
    }

    /**
     * Executes a task that might throw exceptions with standardized handling.
     *
     * @param task The task to execute
     * @param errorMessage The error message to log if an exception occurs
     * @param <T> The return type of the task
     * @return The result of the task, or null if an exception occurs
     * @throws InterruptedException if the thread is interrupted, which should be allowed to propagate
     */
    public static <T> T executeWithExceptionHandling(ExceptionTask<T> task, String errorMessage) throws InterruptedException {
        try {
            return task.execute();
        } catch (InterruptedException e) {
            // Propagate InterruptedException to allow proper script pausing/stopping
            handleInterruptedException(e);
            throw e;
        } catch (Exception e) {
            handleException(e, errorMessage, null);
            return null;
        }
    }

    /**
     * Functional interface for tasks that might throw exceptions.
     *
     * @param <T> The return type of the task
     */
    @FunctionalInterface
    public interface ExceptionTask<T> {
        /**
         * Executes the task.
         *
         * @return The result of the task
         * @throws Exception if an error occurs during execution
         */
        T execute() throws Exception;
    }
}
