package bot.util.string;

import bot.util.logging.Logger;
import rt4.JagString;
import rt4.MiniMenu;

import java.nio.charset.StandardCharsets;

/**
 * Utility class for working with JagStrings
 */
public class JagStringUtils {

    /**
     * Creates a JagString from a regular string
     *
     * @param str The string to convert
     * @return The JagString
     */
    public static JagString toJagString(String str) {
        return JagString.parse(str);
    }

    /**
     * Converts a JagString to a regular string
     *
     * @param jagString The JagString to convert
     * @return The regular string
     */
    public static String toString(JagString jagString) {
        if (jagString == null) {
            return null;
        }
        byte[] chars = new byte[jagString.length];
        for (int i = 0; i < jagString.length; i++) {
            chars[i] = (byte)(jagString.chars[i] & 0xFF);
        }
        return new String(chars, 0, jagString.length);
    }

    /**
     * Checks if a JagString equals a regular string
     *
     * @param jagString The JagString to check
     * @param str The regular string to compare with
     * @return true if they are equal
     */
    public static boolean equals(JagString jagString, String str) {
        if (jagString == null || str == null) {
            return false;
        }
        return toString(jagString).equals(str);
    }

    /**
     * Checks if a JagString equals a regular string, ignoring case
     *
     * @param jagString The JagString to check
     * @param str The regular string to compare with
     * @return true if they are equal, ignoring case
     */
    public static boolean equalsIgnoreCase(JagString jagString, String str) {
        if (jagString == null || str == null) {
            return false;
        }
        return toString(jagString).equalsIgnoreCase(str);
    }

    /**
     * Checks if a JagString contains a regular string
     *
     * @param jagString The JagString to check
     * @param str The regular string to look for
     * @return true if the JagString contains the regular string
     */
    public static boolean contains(JagString jagString, String str) {
        if (jagString == null || str == null) {
            return false;
        }
        return toString(jagString).contains(str);
    }

    /**
     * Checks if a JagString contains a regular string, ignoring case
     *
     * @param jagString The JagString to check
     * @param str The regular string to look for
     * @return true if the JagString contains the regular string, ignoring case
     */
    public static boolean containsIgnoreCase(JagString jagString, String str) {
        if (jagString == null || str == null) {
            return false;
        }
        return toString(jagString).toLowerCase().contains(str.toLowerCase());
    }

    /**
     * Gets all menu options as regular strings
     *
     * @return Array of menu option strings
     */
    public static String[] getMenuOptions() {
        String[] options = new String[MiniMenu.size];
        for (int i = 0; i < MiniMenu.size; i++) {

            JagString op = MiniMenu.getOp(i);
            if (op != null) {
                options[i] = toString(op);
            } else {
                options[i] = "";
            }
        }
        return options;
    }

    /**
     * Finds the index of a menu option containing the specified text
     *
     * @param option The option text to find
     * @return The index of the option, or -1 if not found
     */
    public static int findMenuOption(String option) {
        for (int i = 0; i < MiniMenu.size; i++) {
            JagString op = MiniMenu.ops[i];
            if (equalsIgnoreCase(op, option)) {
                return i;
            }
        }
        return -1;
    }
}
