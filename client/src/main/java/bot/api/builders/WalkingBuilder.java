package bot.api.builders;

import bot.core.ScriptManager;
import bot.api.interaction.WalkInteraction;
import bot.util.game.AnimationUtils;
import bot.util.game.world.Tile;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.util.math.CoordinateUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.function.Predicate;

/**
 * A clean, focused builder for walking operations in the task chaining framework.
 * Provides a chainable API for common walking tasks.
 */
public class WalkingBuilder extends AbstractActionBuilder<WalkingBuilder> {

    private static final Logger logger = LoggerFactory.getLogger(WalkingBuilder.class.getSimpleName());

    // Walking configuration
    private Tile destination = null;
    private Tile[] path = null;
    private int proximityDistance = 3; // How close to get to the destination

    // Path randomization
    private boolean randomizePath = false;
    private int randomizeX = 0;
    private int randomizeY = 0;

    // Destination validation
    private Predicate<Tile> destinationValidator = null;
    private boolean waitUntilNotMoving = true;

    /**
     * Creates a new WalkingBuilder without a specific destination.
     */
    public WalkingBuilder() {
        // Default constructor
    }





    /**
     * Sets the destination tile to walk to.
     *
     * @param destination the destination tile
     * @return this builder for method chaining
     */
    public WalkingBuilder to(Tile destination) {
        this.destination = destination;
        return this;
    }



    /**
     * Sets the destination tile to walk to.
     *
     * @param x the x-coordinate
     * @param y the y-coordinate
     * @param plane the plane (0 = ground level)
     * @return this builder for method chaining
     */
    public WalkingBuilder to(int x, int y, int plane) {
        this.destination = new Tile(x, y, plane);
        return this;
    }

    /**
     * Sets the destination tile to walk to on the ground level.
     *
     * @param x the x-coordinate
     * @param y the y-coordinate
     * @return this builder for method chaining
     */
    public WalkingBuilder to(int x, int y) {
        return to(x, y, 0);
    }

    /**
     * Sets a path of tiles to walk along.
     *
     * @param path the path of tiles to walk
     * @return this builder for method chaining
     */
    public WalkingBuilder alongPath(Tile[] path) {
        this.path = path;
        return this;
    }









    /**
     * Sets how close to get to the destination.
     *
     * @param tiles the distance in tiles
     * @return this builder for method chaining
     */
    public WalkingBuilder withProximity(int tiles) {
        this.proximityDistance = tiles;
        return this;
    }

    /**
     * Configures the builder to randomize the path.
     *
     * @param maxXDeviation the maximum x deviation
     * @param maxYDeviation the maximum y deviation
     * @return this builder for method chaining
     */
    public WalkingBuilder randomize(int maxXDeviation, int maxYDeviation) {
        this.randomizePath = true;
        this.randomizeX = maxXDeviation;
        this.randomizeY = maxYDeviation;
        return this;
    }

    /**
     * Configures the builder to validate the destination before walking.
     *
     * @param validator a predicate that returns true if the destination is valid
     * @return this builder for method chaining
     */
    public WalkingBuilder validateDestination(Predicate<Tile> validator) {
        this.destinationValidator = validator;
        return this;
    }

    /**
     * Configures the builder to wait until the player stops moving.
     *
     * @param wait true to wait, false to continue immediately
     * @return this builder for method chaining
     */
    public WalkingBuilder waitUntilNotMoving(boolean wait) {
        this.waitUntilNotMoving = wait;
        return this;
    }

    @Override
    protected boolean executeAction() throws InterruptedException {
        // Validate configuration
        if (destination == null && path == null) {
            logger.warning("No destination or path specified");
            return false;
        }

        // If we have a destination validator, check if the destination is valid
        if (destination != null && destinationValidator != null && !destinationValidator.test(destination)) {
            logger.warning("Destination failed validation check");
            return false;
        }

        // If we have a single destination, convert it to a path with proper waypoints
        if (destination != null && path == null) {
            path = generateWaypointsToDestination(destination);
            if (path.length == 0) {
                logger.error("Failed to generate waypoints to destination");
                return false;
            }
        }

        // Randomize the path if configured
        Tile[] walkingPath = path;
        if (randomizePath && randomizeX > 0 && randomizeY > 0) {
            walkingPath = randomizePath(path, randomizeX, randomizeY);
        }

        // Walk the path
        logger.info("Walking path with " + walkingPath.length + " waypoints");

        // For single waypoint long distances, use enhanced pathfinding
        if (walkingPath.length == 1) {
            Tile playerLocation = WalkInteraction.getPlayerGlobalLocation();
            if (playerLocation != null) {
                double distance = playerLocation.distanceTo(walkingPath[0]);
                if (distance > 20) {
                    logger.info("Using enhanced pathfinding for long distance: " + String.format("%.1f", distance) + " tiles");
                    boolean walkResult = WalkInteraction.walkToTileWithPathfinding(walkingPath[0]);
                    if (!walkResult) {
                        logger.warning("Enhanced pathfinding failed");
                        return false;
                    }
                } else {
                    boolean walkResult = WalkInteraction.walkPath(walkingPath);
                    if (!walkResult) {
                        logger.warning("Failed to walk path");
                        return false;
                    }
                }
            } else {
                boolean walkResult = WalkInteraction.walkPath(walkingPath);
                if (!walkResult) {
                    logger.warning("Failed to walk path");
                    return false;
                }
            }
        } else {
            boolean walkResult = WalkInteraction.walkPath(walkingPath);
            if (!walkResult) {
                logger.warning("Failed to walk path");
                return false;
            }
        }


        // Wait until the player stops moving if configured
        if (waitUntilNotMoving) {
            logger.info("Waiting for player to stop moving");
            WalkInteraction.waitUntilNotMoving();
        }

        // Add natural delay after walking
        sleep(minDelay, maxDelay);

        // Check if we reached the destination
        Tile playerLoc = WalkInteraction.getPlayerGlobalLocation();
        Tile finalDestination = destination != null ? destination : path[path.length - 1];

        // Ensure finalDestination is in global coordinates
        if (CoordinateUtils.isLocal(finalDestination.getX(), finalDestination.getY())) {
            finalDestination = finalDestination.asGlobal();
        }

        // Calculate distance using the Tile.distanceTo method
        double distance = playerLoc.distanceTo(finalDestination);

        if (distance <= proximityDistance) {
            logger.info("Successfully reached destination (distance: " + String.format("%.1f", distance) + " tiles)");
            return true;
        } else {
            logger.warning("Failed to reach destination, still " + String.format("%.1f", distance) + " tiles away");
            return false;
        }
    }

    /**
     * Randomizes a path of tiles.
     *
     * @param path the path to randomize
     * @param maxXDeviation the maximum x deviation
     * @param maxYDeviation the maximum y deviation
     * @return the randomized path
     */
    private Tile[] randomizePath(Tile[] path, int maxXDeviation, int maxYDeviation) {
        List<Tile> randomized = new ArrayList<>();

        for (Tile tile : path) {
            // Generate random offsets
            int xOffset = random.nextInt(maxXDeviation * 2 + 1) - maxXDeviation;
            int yOffset = random.nextInt(maxYDeviation * 2 + 1) - maxYDeviation;

            // Create a new tile with the offsets
            randomized.add(new Tile(
                tile.getX() + xOffset,
                tile.getY() + yOffset,
                tile.getPlane()
            ));
        }

        return randomized.toArray(new Tile[0]);
    }

    /**
     * Generates waypoints from the player's current location to the destination.
     * For long distances, this breaks the path into smaller waypoints suitable for minimap walking.
     *
     * @param destination the final destination
     * @return an array of waypoints leading to the destination
     */
    private Tile[] generateWaypointsToDestination(Tile destination) {
        Tile playerLocation = WalkInteraction.getPlayerGlobalLocation();
        if (playerLocation == null) {
            logger.error("Could not get player location. Cannot generate waypoints.");
            return new Tile[0]; // Return empty array to indicate failure
        }

        // Ensure destination is in global coordinates
        Tile globalDestination = destination;
        if (destination.getX() < 1000 && destination.getY() < 1000) {
            globalDestination = destination.asGlobal();
        }

        double totalDistance = playerLocation.distanceTo(globalDestination);

        // For short distances, use direct path
        if (totalDistance <= 15) {
            return new Tile[] { globalDestination };
        }

        // For longer distances, generate waypoints every 12-15 tiles
        List<Tile> waypoints = new ArrayList<>();

        int deltaX = globalDestination.getX() - playerLocation.getX();
        int deltaY = globalDestination.getY() - playerLocation.getY();

        // Calculate number of waypoints needed (target ~12 tiles apart)
        int numWaypoints = Math.max(1, (int) Math.ceil(totalDistance / 12.0));

        // Generate intermediate waypoints
        for (int i = 1; i < numWaypoints; i++) {
            double progress = (double) i / numWaypoints;
            int waypointX = playerLocation.getX() + (int) (deltaX * progress);
            int waypointY = playerLocation.getY() + (int) (deltaY * progress);

            waypoints.add(new Tile(waypointX, waypointY, globalDestination.getPlane()));
        }

        // Add final destination
        waypoints.add(globalDestination);

        logger.info("Generated " + waypoints.size() + " waypoints for " + String.format("%.1f", totalDistance) + " tile journey");
        return waypoints.toArray(new Tile[0]);
    }

}
