package bot.core;

import bot.api.script.BotScript;
import bot.core.thread.BotThread;
import bot.util.event.EventBus;
import bot.util.event.events.ScriptStartEvent;
import bot.util.event.events.ScriptStopEvent;
import bot.util.event.events.ScriptProgressEvent;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.ui.account.Account;
import bot.ui.account.AccountManager;
import bot.ui.settings.SettingsManager;

import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Main class for managing scripts
 * This class is responsible for starting, stopping, and tracking scripts
 * It is completely independent of the UI and client implementation
 */
public class ScriptManager {
    // Singleton instance
    private static final ScriptManager instance = new ScriptManager();

    // Thread-safe references to the current thread and script
    private final AtomicReference<BotThread> currentThread = new AtomicReference<>();
    private final AtomicReference<BotScript> currentScript = new AtomicReference<>();

    // Use a lock for operations that need to be atomic but involve multiple steps
    private final ReentrantLock scriptOperationLock = new ReentrantLock();

    // Logger for script management events
    private final Logger logger = LoggerFactory.getLogger(ScriptManager.class);

    // Track script runtime with atomic reference for thread safety
    private final AtomicLong scriptStartTime = new AtomicLong(0);

    /**
     * Gets the singleton instance of the ScriptManager
     *
     * @return The ScriptManager instance
     */
    public static ScriptManager getInstance() {
        return instance;
    }

    /**
     * Private constructor to enforce singleton pattern
     * No dependencies on other components
     */
    private ScriptManager() {
        logger.info("ScriptManager initialized");
    }

    /**
     * Checks if the player is actually in-game (not just in lobby)
     * Uses the same logic as the BotUI to determine login status
     *
     * @return true if the player is in-game
     */
    private boolean isPlayerInGame() {
        try {
            // Check if game state is logged in AND player object exists
            return bot.util.game.GameState.isLoggedIn() && rt4.PlayerList.self != null;
        } catch (Exception e) {
            // If there's any error checking, assume not logged in
            return false;
        }
    }

    /**
     * Starts a script with the specified parameters
     *
     * @param script The script to run
     * @param loopDelay The delay between loop iterations in milliseconds (default: 100ms)
     * @param adjustCamera Whether to adjust the camera angle before starting the script (default: from settings)
     * @param autoLogin Whether to automatically log in with the selected account (default: from settings)
     * @return true if the script was started
     * @throws IllegalArgumentException if script is null or loopDelay is negative
     */
    public boolean startScript(BotScript script, long loopDelay, boolean adjustCamera, boolean autoLogin) {
        // Validate parameters
        if (script == null) {
            throw new IllegalArgumentException("Script cannot be null");
        }
        if (loopDelay < 0) {
            throw new IllegalArgumentException("Loop delay cannot be negative");
        }

        // Check if player is actually in-game
        if (!isPlayerInGame()) {
            logger.warning("Cannot start script: Player is not logged in to the game. Please log in first or enable auto-login.");
            return false;
        }

        // Use a lock to ensure thread safety for the entire operation
        scriptOperationLock.lock();
        try {
        // Stop any running script
        stopScript();

        // Set the current script
        currentScript.set(script);

        // Record the script start time
        scriptStartTime.set(System.currentTimeMillis());

        // Auto-login with selected account if requested
        if (autoLogin) {
            Account selectedAccount = AccountManager.getInstance().getSelectedAccount();
            if (selectedAccount != null) {
                // Always attempt to log in when auto-login is enabled
                // This ensures we handle all cases: login screen, title screen, and lobby
                try {
                    boolean loginSuccess = LoginManager.login(selectedAccount);
                    if (!loginSuccess) {
                        logger.warning("Failed to log in with account: " + selectedAccount.getUsername());
                        // Continue with script even if login fails
                    }
                } catch (InterruptedException e) {
                    logger.error("Login interrupted", e);
                    Thread.currentThread().interrupt();
                    return false;
                }
            } else {
                logger.warning("Auto-login enabled but no account selected");
            }
        } else {
            logger.info("Auto-login disabled, skipping login");
        }

        // Adjust camera angle before starting script if requested
        if (adjustCamera) {
            logger.info("Auto-adjust camera enabled, adjusting camera angle...");
            // Use the new method to adjust the camera
            adjustCameraAndWait();
        } else {
            logger.info("Auto-adjust camera disabled, skipping camera adjustment");
        }

        try {
            // Create and start a new thread for the script
            // This happens AFTER camera adjustment is fully complete
            BotThread thread = new BotThread(script, loopDelay);
            currentThread.set(thread);
            thread.start();

            // Publish script start event
            String scriptName = ((Object)script).getClass().getSimpleName();
            logger.info("Started script: " + scriptName + " with loop delay: " + loopDelay + "ms" +
                       ", auto-adjust camera: " + adjustCamera + ", auto-login: " + autoLogin);

            // Publish the event in a try-catch to prevent exceptions from affecting script startup
            try {
                EventBus.getInstance().publish(new ScriptStartEvent(script));
            } catch (Exception e) {
                logger.error("Error publishing script start event", e);
                // Continue despite event publishing error
            }

            return true;
        } catch (Exception e) {
            // Handle any unexpected exceptions during thread creation/starting
            logger.error("Error starting script thread", e);
            // Clean up in case of error
            currentScript.set(null);
            scriptStartTime.set(0);
            return false;
        }
        } finally {
            scriptOperationLock.unlock();
        }
    }

    /**
     * Starts a script with default settings from SettingsManager
     *
     * @param script The script to run
     * @return true if the script was started
     * @throws IllegalArgumentException if script is null
     */
    public boolean startScript(BotScript script) {
        if (script == null) {
            throw new IllegalArgumentException("Script cannot be null");
        }

        // Check if player is actually in-game
        if (!isPlayerInGame()) {
            logger.warning("Cannot start script: Player is not logged in to the game. Please log in first or enable auto-login.");
            return false;
        }

        boolean autoLogin = SettingsManager.getAutoLogin();

        return startScript(
            script,
            100, // Default loop delay of 100ms
            SettingsManager.getAutoAdjustCamera(),
            autoLogin
        );
    }

    /**
     * Adjusts the camera angle and waits for it to complete
     * This method blocks until the camera adjustment is complete
     *
     * @return true if the camera adjustment completed successfully
     */
    public boolean adjustCameraAndWait() {
        logger.info("Adjusting camera angle...");

        // Create a countdown latch to synchronize with the camera adjustment thread
        final CountDownLatch cameraAdjustmentLatch = new CountDownLatch(1);

        // Create a dedicated thread for camera adjustment
        Thread cameraThread = new Thread(() -> {
            try {
                // Perform camera adjustment in a dedicated thread
                adjustCameraAngleBlocking();
                logger.info("Camera adjustment thread completed successfully");
            } finally {
                // Signal that camera adjustment is complete, even if an exception occurred
                cameraAdjustmentLatch.countDown();
            }
        }, "CameraAdjustmentThread");

        // Start the camera adjustment thread
        cameraThread.start();

        try {
            // Wait for the camera adjustment to complete with a timeout
            // Use a longer timeout (5 seconds) to ensure we give enough time
            boolean completed = cameraAdjustmentLatch.await(5, TimeUnit.SECONDS);

            if (completed) {
                logger.info("Camera adjustment completed successfully");
                // Add a small buffer after camera adjustment
                Thread.sleep(500);
                return true;
            } else {
                logger.debug("Camera adjustment timed out after 5 seconds");
                return false;
            }
        } catch (InterruptedException e) {
            logger.error("Interrupted while waiting for camera adjustment to complete", e);
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * Stops the current script
     *
     * @return true if a script was stopped
     */
    public boolean stopScript() {
        scriptOperationLock.lock();
        try {
        BotThread thread = currentThread.get();
        if (thread != null && thread.isRunning()) {
            // If the script is paused, resume it first to ensure clean shutdown
            if (thread.isPaused()) {
                thread.resumeBot();
                logger.info("Resumed paused script before stopping");
            }

            // First, try to stop the thread gracefully
            thread.stopBot();
            logger.debug("Stopping script, waiting for thread to terminate...");

            try {
                // Wait up to 2 seconds for the thread to stop gracefully
                thread.join(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("Interrupted while stopping script", e);
            }

            // If the thread is still running after the timeout, force it to stop
            if (thread.isAlive()) {
                logger.warning("Thread did not stop gracefully, forcing interruption");
                thread.interrupt(); // Interrupt again to be sure

                try {
                    // Wait up to 3 more seconds for the thread to stop after forced interruption
                    thread.join(3000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.error("Interrupted while forcing script to stop", e);
                }

                // If the thread is STILL running, log a warning
                if (thread.isAlive()) {
                    logger.warning("Thread could not be stopped, it may continue running in the background");
                }
            }

            BotScript script = currentScript.get();
            if (script != null) {
                logger.info("Stopped script: " + ((Object)script).getClass().getSimpleName());
            }

            BotScript stoppedScript = currentScript.get();

            // Clear references first
            currentThread.set(null);
            currentScript.set(null);
            scriptStartTime.set(0);

            // Publish script stop event after clearing references
            if (stoppedScript != null) {
                try {
                    // Calculate the runtime of the script
                    long runTime = System.currentTimeMillis() - scriptStartTime.get();
                    EventBus.getInstance().publish(new ScriptStopEvent(stoppedScript, runTime));
                } catch (Exception e) {
                    logger.error("Error publishing script stop event", e);
                    // Continue despite event publishing error
                }
            }

            return true;
        }
        return false;
        } finally {
            scriptOperationLock.unlock();
        }
    }

    /**
     * Pauses the current script
     *
     * @return true if a script was paused
     */
    public boolean pauseScript() {
        scriptOperationLock.lock();
        try {
        BotThread thread = currentThread.get();
        if (thread != null && thread.isRunning() && !thread.isPaused()) {
            thread.pauseBot();
            logger.info("Paused script: " + ((Object)currentScript.get()).getClass().getSimpleName());
            return true;
        }
        return false;
        } finally {
            scriptOperationLock.unlock();
        }
    }

    /**
     * Resumes the current script
     *
     * @return true if a script was resumed
     */
    public boolean resumeScript() {
        scriptOperationLock.lock();
        try {
        BotThread thread = currentThread.get();
        if (thread != null && thread.isRunning() && thread.isPaused()) {
            thread.resumeBot();
            logger.info("Resumed script: " + ((Object)currentScript.get()).getClass().getSimpleName());
            return true;
        }
        return false;
        } finally {
            scriptOperationLock.unlock();
        }
    }

    /**
     * Checks if the current script is paused
     *
     * @return true if a script is paused
     */
    public boolean isScriptPaused() {
        BotThread thread = currentThread.get();
        return thread != null && thread.isPaused();
    }

    /**
     * Checks if a script is running
     *
     * @return true if a script is running
     */
    public boolean isScriptRunning() {
        BotThread thread = currentThread.get();
        return thread != null && thread.isRunning();
    }

    /**
     * Gets the current script
     *
     * @return The current script, or null if no script is running
     */
    public BotScript getCurrentScript() {
        return currentScript.get();
    }

    /**
     * Gets the logger
     *
     * @return The logger
     */
    public Logger getLogger() {
        return logger;
    }

    /**
     * Gets the script runtime in milliseconds
     *
     * @return The script runtime in milliseconds, or 0 if no script is running
     */
    public long getScriptRuntime() {
        long startTime = scriptStartTime.get();
        if (startTime == 0 || !isScriptRunning()) {
            return 0;
        }
        return System.currentTimeMillis() - startTime;
    }

    /**
     * Gets the script runtime formatted as HH:MM:SS
     *
     * @return The formatted script runtime, or "00:00:00" if no script is running
     */
    public String getFormattedScriptRuntime() {
        long runtime = getScriptRuntime();
        if (runtime == 0) {
            return "00:00:00";
        }

        long seconds = runtime / 1000;
        long hours = seconds / 3600;
        seconds %= 3600;
        long minutes = seconds / 60;
        seconds %= 60;

        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }

    /**
     * Non-blocking version of camera adjustment that returns immediately
     * This is used when we want to adjust the camera without waiting
     */
    private void adjustCameraAngle() {
        // Create a new thread to adjust the camera
        Thread cameraThread = new Thread(this::adjustCameraAngleBlocking, "CameraAdjustmentThread");
        cameraThread.start();
        logger.info("Started camera adjustment in background thread");
    }

    /**
     * Adjusts the camera angle to a top-down view and blocks until complete
     * This helps scripts that rely on minimap navigation
     * Uses direct camera control API instead of simulating keyboard input
     */
    private void adjustCameraAngleBlocking() {
        try {
            logger.info("Adjusting camera angle using direct camera control API");

            // Get the current camera pitch
            double currentPitch = rt4.Camera.pitchTarget;
            logger.info("Current camera pitch: " + currentPitch);

            // Set the target pitch to the maximum (383 is the maximum allowed pitch)
            // This will give us a top-down view
            double targetPitch = 383;
            logger.info("Setting camera pitch to: " + targetPitch);

            // Use the API to set the camera pitch directly
            plugin.api.API.SetCameraPitch(targetPitch);

            // Wait for the camera to adjust (the game will smoothly animate to the target)
            // We'll check the pitch periodically to see when it's close to the target
            long startTime = System.currentTimeMillis();
            long timeout = 5000; // 5 second timeout

            while (Math.abs(rt4.Camera.pitchTarget - targetPitch) > 5) {
                // Check if we've timed out
                if (System.currentTimeMillis() - startTime > timeout) {
                    logger.debug("Camera adjustment timed out after " + timeout + "ms");
                    break;
                }

                // Check if thread was interrupted
                if (Thread.currentThread().isInterrupted()) {
                    logger.debug("Camera adjustment interrupted before completion");
                    break;
                }

                // Sleep a bit before checking again
                Thread.sleep(100);
            }

            // Add a small delay to let the camera settle
            Thread.sleep(500);

            // Calculate and log the actual time spent adjusting camera
            long actualTime = System.currentTimeMillis() - startTime;
            logger.info("Camera angle adjustment complete (actual time: " + actualTime + "ms)");
            logger.info("Final camera pitch: " + rt4.Camera.pitchTarget);
        } catch (InterruptedException e) {
            logger.error("Failed to adjust camera angle", e);
            // Interrupt current thread if interrupted
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Reports script progress
     *
     * @param status The current status of the script
     * @param progress The current progress of the script (0-100)
     */
    public void reportProgress(String status, int progress) {
        if (status == null) {
            status = "Unknown"; // Provide a default value if null
        }

        // Validate progress range
        int validProgress = Math.max(0, Math.min(100, progress));
        if (validProgress != progress) {
            logger.warning("Progress value " + progress + " out of range, clamped to " + validProgress);
        }

        BotScript script = currentScript.get();
        if (script != null) {
            try {
                EventBus.getInstance().publish(new ScriptProgressEvent(script, status, validProgress));
            } catch (Exception e) {
                logger.error("Error publishing script progress event", e);
            }
        }
    }


