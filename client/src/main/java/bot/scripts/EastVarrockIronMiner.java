package bot.scripts;

import bot.api.interaction.BankingInteraction;
import bot.api.interaction.InventoryInteraction;
import bot.api.builders.SceneryInteractionBuilder;
import bot.api.builders.WalkingBuilder;
import bot.util.game.AnimationUtils;
import bot.util.entity.EntityUtils;
import bot.util.game.entity.Player;
import bot.util.game.world.Tile;
import bot.util.RandomUtils;
import bot.api.script.BotScript;
import bot.api.script.ScriptCategory;
import bot.api.script.ScriptManifest;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.util.stats.XPTracker;
import bot.ui.paint.PaintProvider;
import bot.ui.paint.PaintMetric;
import bot.ui.paint.metrics.ScriptMetrics;

import java.util.List;
import rt4.Scenery;
import bot.util.game.entity.Item;

import java.util.ArrayList;

/**
 * A script that mines iron ore at East Varrock and banks it.
 * Waits for animations to change before moving to the next ore.
 */
@ScriptManifest(
        name = "East Varrock Iron Miner",
        version = 1.0,
        description = "Mines iron ore at East Varrock and banks it",
        authors = {"Augment"},
        category = ScriptCategory.SKILLING,
        keywords = {"mining", "iron", "varrock", "banking"}
)
public class EastVarrockIronMiner implements BotScript, PaintProvider {
    private static final Logger logger = LoggerFactory.getLogger(EastVarrockIronMiner.class);

    // Script states
    private enum State {
        MINING,
        WALKING_TO_BANK,
        BANKING,
        WALKING_TO_MINE
    }

    // Iron ore rock IDs - these will be replaced with correct IDs
    //private static final int[] IRON_ROCK_IDS = {11954, 11955, 11956};

    private static final int[] IRON_ROCK_IDS = {11961, 11957};

    // Pickaxe IDs - these will be replaced with correct IDs
    private static final int[] PICKAXE_IDS = {1266, 1267, 1269, 1271, 1273, 1275, 1276};

    // Iron ore ID - this will be replaced with correct ID
    //private static final int IRON_ORE_ID = 440;

    private static final int[] IRON_ORE_ID = {437, 439};

    // Skill ID
    private static final int MINING_SKILL_ID = 14;

    Tile[] pathToBank = {
            new Tile(3286, 3365, 0),
            new Tile(3286, 3365, 0),
            new Tile(3290, 3374, 0),
            new Tile(3290, 3383, 0),
            new Tile(3290, 3392, 0),
            new Tile(3290, 3401, 0),
            new Tile(3286, 3410, 0),
            new Tile(3280, 3418, 0),
            new Tile(3274, 3426, 0),
            new Tile(3265, 3426, 0),
            new Tile(3256, 3428, 0),
            new Tile(3256, 3428, 0),
            new Tile(3256, 3428, 0),
            new Tile(3253, 3423, 0),
            new Tile(3253, 3423, 0),
            new Tile(3253, 3420, 0)
    };

    // Location constants
    private static final Tile BANK_LOCATION = new Tile(3253, 3420, 0); // East Varrock bank
    private static final Tile MINE_LOCATION = new Tile(3286, 3365, 0); // East Varrock iron mine

    // Distance thresholds
    private static final int NEAR_DISTANCE = 10; // Distance in tiles to consider "near" a location

    // Current state of the script
    private State currentState = State.MINING;

    // Bank booth ID - this will be replaced with correct ID
    private static final int BANK_BOOTH_ID = 11402;

    // Statistics tracking
    private ScriptMetrics metrics;
    private int startOre;

    @Override
    public void onStart() {
        logger.info("East Varrock Iron Miner started");

        // Check if player has a pickaxe
        if (!hasPickaxe()) {
            logger.error("No pickaxe found in inventory. Please equip a pickaxe before starting.");
            // Stop the script
            bot.core.ScriptManager.getInstance().stopScript();
            return;
        }

        logger.info("Pickaxe detected - ready to mine!");

        // Initialize metrics tracking
        metrics = new ScriptMetrics(MINING_SKILL_ID);
        metrics.setStatus("Starting...");

        logger.info("Statistics tracking initialized");

        // Start with a default state - updateStateIfNeeded will fix it
        currentState = State.MINING;
    }

    @Override
    public boolean onLoop() throws InterruptedException {
        // Check if we need to update the state based on current conditions
        updateStateIfNeeded();

        // Update status based on current state
        switch (currentState) {
            case MINING:
                metrics.setStatus("Mining iron ore");
                return mineIronOre();

            case WALKING_TO_BANK:
                metrics.setStatus("Walking to bank");
                logger.info("Walking to bank");
                boolean reachedBank = walkToBank();

                if (reachedBank) {
                    logger.info("Reached bank");
                    currentState = State.BANKING;
                }

                return true;

            case BANKING:
                metrics.setStatus("Banking ores");
                logger.info("Banking items");
                boolean bankingComplete = bankItems();

                if (bankingComplete) {
                    logger.info("Banking complete, walking back to mine");
                    currentState = State.WALKING_TO_MINE;
                }

                return true;

            case WALKING_TO_MINE:
                metrics.setStatus("Walking to mine");
                logger.info("Walking back to mine");
                boolean reachedMine = walkToMine();

                if (reachedMine) {
                    logger.info("Reached mine");
                    currentState = State.MINING;
                }

                return true;

            default:
                logger.error("Unknown state: " + currentState);
                return false;
        }
    }

    @Override
    public void onStop() {
        logger.info("East Varrock Iron Miner stopped");


    }



    /**
     * Checks if the player is near the bank
     *
     * @param playerLocation The player's current location
     * @return true if the player is near the bank
     */
    private boolean isNearBank(Tile playerLocation) {
        return playerLocation.distanceTo(BANK_LOCATION) < NEAR_DISTANCE;
    }

    /**
     * Checks if the player is near the mine
     *
     * @param playerLocation The player's current location
     * @return true if the player is near the mine
     */
    private boolean isNearMine(Tile playerLocation) {
        return playerLocation.distanceTo(MINE_LOCATION) < NEAR_DISTANCE;
    }

    /**
     * Checks if the inventory is empty (except for pickaxe)
     *
     * @return true if the inventory only contains a pickaxe
     */
    private boolean isInventoryEmpty() {
        // Count total items in inventory
        int totalItems = 0;
        int pickaxeCount = 0;

        for (int i = 0; i < 28; i++) { // Check all inventory slots
            Item item = InventoryInteraction.getItem(i);
            if (item != null) {
                totalItems++;
                if (isPickaxe(item.getId())) {
                    pickaxeCount++;
                }
            }
        }

        // Inventory is empty if it only contains pickaxes
        return totalItems == pickaxeCount;
    }

    /**
     * Updates the state if needed based on current conditions
     * This helps handle situations where the player might be moved by random events
     * or other factors during script execution
     */
    private void updateStateIfNeeded() {
        // Get the player's current location
        Tile playerLocation = Player.getLocation();
        boolean inventoryFull = InventoryInteraction.isInventoryFull();

        // Always check conditions in priority order, regardless of current state

        // Priority 1: If inventory is full, we need to bank
        if (inventoryFull) {
            if (isNearBank(playerLocation)) {
                // We're at bank and inventory is full - bank
                if (currentState != State.BANKING) {
                    currentState = State.BANKING;
                    logger.info("At bank with full inventory - banking");
                }
            } else {
                // We're not at bank and inventory is full - walk to bank
                if (currentState != State.WALKING_TO_BANK) {
                    currentState = State.WALKING_TO_BANK;
                    logger.info("Inventory full - walking to bank");
                }
            }
            return;
        }

        // Priority 2: If inventory is not full, we should be mining
        if (isNearMine(playerLocation)) {
            // We're at mine and inventory not full - mine
            if (currentState != State.MINING) {
                currentState = State.MINING;
                logger.info("At mine with space - mining");
            }
        } else {
            // We're not at mine and inventory not full - walk to mine
            if (currentState != State.WALKING_TO_MINE) {
                currentState = State.WALKING_TO_MINE;
                logger.info("Inventory has space - walking to mine");
            }
        }
    }

    /**
     * Checks if the player has a pickaxe in their inventory
     *
     * @return true if the player has a pickaxe
     */
    private boolean hasPickaxe() {
        for (int pickaxeId : PICKAXE_IDS) {
            if (InventoryInteraction.inventoryContains(pickaxeId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Mines iron ore
     *
     * @return true if the mining action was successful
     * @throws InterruptedException if the thread is interrupted
     */
    private boolean mineIronOre() throws InterruptedException {
        // Find the nearest iron rock
        Scenery ironRock = EntityUtils.findNearestScenery(IRON_ROCK_IDS);

        if (ironRock == null) {
            logger.info("No iron rocks found nearby, waiting...");
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                logger.error("Interrupted while waiting for rocks", e);
                return false;
            }
            return true;
        }

        logger.info("Found iron rock, mining...");

        // Use the SceneryInteractionBuilder to mine the rock
        // This will handle walking to the rock and interacting with it
        boolean miningStarted = false;
        // Use the SceneryInteractionBuilder with the array of IDs

            miningStarted = new SceneryInteractionBuilder().with(IRON_ROCK_IDS) // Use the builder pattern
                .action("Mine")
                .withDelay(300, 500)
                .execute();
            // Let InterruptedException propagate to properly handle script pausing/stopping

            if (!miningStarted) {
                logger.error("Failed to start mining");
                return false;
            }


        // Wait for the animation to change from -1 to something else (mining started)
        if (AnimationUtils.getAnimation() == -1) {
            logger.info("Waiting for mining animation to start");

                boolean animationStarted = AnimationUtils.waitForAnimationToStart();
                if (!animationStarted) {
                    logger.error("Mining animation didn't start");
                    return false;
                }
            }


        // Wait for the animation to change back to -1 (mining completed)
        logger.info("Mining in progress, waiting for completion");

            boolean animationCompleted = AnimationUtils.waitForAnimationToComplete(10000);
            if (!animationCompleted) {
                logger.error("Mining animation didn't complete");
                return false;
            }



        logger.info("Mining completed");
        // Track iron ore gained
        int currentOre = 0;
        for (int oreId : IRON_ORE_ID) {
            currentOre += InventoryInteraction.getInventoryItemCount(oreId);
        }
        int previousOre = metrics.get("iron_ore_in_inventory");
        if (currentOre > previousOre) {
            int gained = currentOre - previousOre;
            metrics.increment("iron_ore", gained);
            metrics.set("iron_ore_in_inventory", currentOre);
        }

        // Add a small delay before looking for the next rock
        try {
            Thread.sleep(RandomUtils.random(500, 800));
        } catch (InterruptedException e) {
            logger.error("Interrupted while sleeping", e);
            return false;
        }

        return true;
    }

    /**
     * Walks to the bank
     *
     * @return true if the player reached the bank
     * @throws InterruptedException if the thread is interrupted
     */
    private boolean walkToBank() throws InterruptedException {
        // Use the WalkingBuilder to walk to the bank

            return new WalkingBuilder()
                    .alongPath(pathToBank)
                    .withDelay(500, 800)
                    .execute();

    }

    /**
     * Banks items
     *
     * @return true if banking was successful
     * @throws InterruptedException if the thread is interrupted
     */
    private boolean bankItems() throws InterruptedException {
        // Find the nearest bank booth
        Scenery bankBooth = EntityUtils.findNearestScenery(BANK_BOOTH_ID);

        if (bankBooth == null) {
            logger.error("No bank booth found nearby");
            return false;
        }

        // Use the BankInteractionBuilder to handle banking operations
        logger.info("Using BankInteractionBuilder for banking operations");

        // First, use SceneryInteractionBuilder to open the bank
        logger.info("Using SceneryInteractionBuilder to open the bank");
        int bankBoothId = (int)(bankBooth.key >> 32 & 0x7fffffff); // Get the ID from the scenery object

            boolean bankOpened = new SceneryInteractionBuilder().with(bankBoothId)
                .action("Use-quickly")
                .withDelay(300, 500)
                .execute();

            if (!bankOpened) {
                logger.error("Failed to open bank");
                return false;
            }


        // Wait for the bank interface to open
        Thread.sleep(RandomUtils.random(1000, 1500));
        // Let InterruptedException propagate to properly handle script pausing/stopping

        // Now use BankInteractionBuilder for banking operations
        bot.api.builders.BankInteractionBuilder bankBuilder = new bot.api.builders.BankInteractionBuilder()
            .withDelay(300, 500);

        // Get all item IDs to deposit (all items except pickaxes)
        List<Integer> itemsToDeposit = new ArrayList<>();
        for (int i = 0; i < 28; i++) { // Check all inventory slots
            Item item = InventoryInteraction.getItem(i);
            if (item != null && !isPickaxe(item.getId())) {
                itemsToDeposit.add(item.getId());
            }
        }

        // Use the deposit method with all items to deposit
        if (!itemsToDeposit.isEmpty()) {
            int[] itemIds = itemsToDeposit.stream().mapToInt(Integer::intValue).toArray();
            bankBuilder.deposit(itemIds).withDepositAction("Deposit-All");
        }

        // Execute the banking operations
        boolean bankingSuccess = bankBuilder.execute();

        if (!bankingSuccess) {
            logger.error("Banking operations failed");
            return false;
        }


        // Wait for the deposit to complete
        Thread.sleep(RandomUtils.random(500, 800));
        // Let InterruptedException propagate to properly handle script pausing/stopping

        // Close the bank
        logger.info("Closing bank");
        BankingInteraction.close();

        // Wait for the bank interface to close
        try {
            Thread.sleep(RandomUtils.random(500, 800));
        } catch (InterruptedException e) {
            logger.error("Interrupted while waiting for bank to close", e);
            return false;
        }

        metrics.increment("banking_trips");
        // Reset inventory tracking since we banked
        metrics.set("iron_ore_in_inventory", 0);
        return true;
    }

    /**
     * Walks back to the mine
     *
     * @return true if the player reached the mine
     * @throws InterruptedException if the thread is interrupted
     */
    private boolean walkToMine() throws InterruptedException {
        // Create a reversed path to walk back to the mine
        Tile[] pathToMine = new Tile[pathToBank.length];

        for (int i = 0; i < pathToBank.length; i++) {
            pathToMine[i] = pathToBank[pathToBank.length - 1 - i];
        }

        // Use the WalkingBuilder to walk back to the mine

            return new WalkingBuilder()
                    .alongPath(pathToMine)
                    .withDelay(500, 800)
                    .execute();

    }

    /**
     * Checks if an item ID is a pickaxe
     *
     * @param itemId The item ID to check
     * @return true if the item is a pickaxe
     */
    private boolean isPickaxe(int itemId) {
        for (int pickaxeId : PICKAXE_IDS) {
            if (itemId == pickaxeId) {
                return true;
            }
        }

        return false;
    }

   

    @Override
    public java.util.Map<String, PaintMetric> getPaintMetrics() {
        if (metrics == null) {
            return new java.util.HashMap<>();
        }
        return metrics.generatePaintMetrics();
    }
}
