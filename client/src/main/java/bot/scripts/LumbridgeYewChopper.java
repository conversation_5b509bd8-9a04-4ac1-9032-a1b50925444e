package bot.scripts;

import bot.api.interaction.InventoryInteraction;
import bot.api.builders.SceneryInteractionBuilder;
import bot.api.builders.BankInteractionBuilder;
import bot.api.builders.WalkingBuilder;
import bot.util.game.AnimationUtils;
import bot.util.entity.EntityUtils;
import bot.util.game.world.Tile;
import bot.util.game.entity.Player;
import bot.util.RandomUtils;
import bot.api.script.BotScript;
import bot.api.script.ScriptCategory;
import bot.api.script.ScriptManifest;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.util.stats.XPTracker;
import bot.ui.paint.PaintManager;
import bot.ui.paint.PaintProvider;
import bot.ui.paint.PaintMetric;
import bot.ui.paint.metrics.ScriptMetrics;

import java.util.List;
import rt4.Scenery;

/**
 * A script that chops yew trees behind Lumbridge castle and banks the logs in Draynor.
 * Requires level 60+ Woodcutting and an axe.
 * Efficiently banks yew logs while keeping the axe equipped/in inventory.
 */
@ScriptManifest(
        name = "Lumbridge Yew Chopper",
        version = 1.0,
        description = "Chops yew trees behind Lumbridge castle and banks in Draynor",
        authors = {"Augment"},
        category = ScriptCategory.SKILLING,
        keywords = {"woodcutting", "yew", "lumbridge", "draynor", "banking"}
)
public class LumbridgeYewChopper implements BotScript, PaintProvider {
    private static final Logger logger = LoggerFactory.getLogger(LumbridgeYewChopper.class);

    // Script states
    private enum State {
        CHOPPING,
        WALKING_TO_BANK,
        BANKING,
        WALKING_TO_TREES
    }

    // Constants
    private static final int WOODCUTTING_SKILL_ID = 8;
    private static final int REQUIRED_WOODCUTTING_LEVEL = 60;
    
    private static final int[] YEW_TREE_IDS = {1309}; // Common yew tree IDs
    private static final int YEW_LOGS_ID = 1516;
    
    // Axe IDs for checking if player has an axe
    private static final int[] AXE_IDS = {1352, 1350, 1354, 1356, 1358, 1360, 1349}; // Bronze to Dragon
    
    private static final int BANK_BOOTH_ID = 2213; // Common bank booth ID
    
    // Locations
    private static final Tile YEW_TREES_LOCATION = new Tile(3189, 3229, 0); // Behind Lumbridge castle
    private static final Tile DRAYNOR_BANK_LOCATION = new Tile(3092, 3243, 0); // Draynor bank
    
    // Path from yew trees to Draynor bank
    private final Tile[] pathToBank = {
        new Tile(3189, 3229, 0), // Yew trees
        new Tile(3185, 3229, 0),
        new Tile(3180, 3229, 0),
        new Tile(3175, 3229, 0),
        new Tile(3170, 3229, 0),
        new Tile(3165, 3229, 0),
        new Tile(3160, 3229, 0),
        new Tile(3155, 3229, 0),
        new Tile(3150, 3229, 0),
        new Tile(3145, 3229, 0),
        new Tile(3140, 3229, 0),
        new Tile(3135, 3229, 0),
        new Tile(3130, 3229, 0),
        new Tile(3125, 3229, 0),
        new Tile(3120, 3229, 0),
        new Tile(3115, 3229, 0),
        new Tile(3110, 3229, 0),
        new Tile(3105, 3229, 0),
        new Tile(3100, 3235, 0),
        new Tile(3095, 3240, 0),
        new Tile(3092, 3243, 0)  // Draynor bank
    };
    
    // Path from Draynor bank to yew trees
    private final Tile[] pathToTrees = {
        new Tile(3092, 3243, 0), // Draynor bank
        new Tile(3095, 3240, 0),
        new Tile(3100, 3235, 0),
        new Tile(3105, 3229, 0),
        new Tile(3110, 3229, 0),
        new Tile(3115, 3229, 0),
        new Tile(3120, 3229, 0),
        new Tile(3125, 3229, 0),
        new Tile(3130, 3229, 0),
        new Tile(3135, 3229, 0),
        new Tile(3140, 3229, 0),
        new Tile(3145, 3229, 0),
        new Tile(3150, 3229, 0),
        new Tile(3155, 3229, 0),
        new Tile(3160, 3229, 0),
        new Tile(3165, 3229, 0),
        new Tile(3170, 3229, 0),
        new Tile(3175, 3229, 0),
        new Tile(3180, 3229, 0),
        new Tile(3185, 3229, 0),
        new Tile(3189, 3229, 0)  // Yew trees
    };

    // Script state
    private State currentState = State.CHOPPING;

    // Statistics tracking
    private ScriptMetrics metrics;
    private int startLogs;

    @Override
    public void onStart() {
        logger.info("Lumbridge Yew Chopper started");

        // Check woodcutting level requirement
        int woodcuttingLevel = Player.getBaseLevel(WOODCUTTING_SKILL_ID);
        if (woodcuttingLevel < REQUIRED_WOODCUTTING_LEVEL) {
            logger.error("Woodcutting level " + woodcuttingLevel + " is too low. Requires level " + REQUIRED_WOODCUTTING_LEVEL + "+");
            bot.core.ScriptManager.getInstance().stopScript();
            return;
        }

        // Check if player has an axe
        if (!hasAxe()) {
            logger.error("No axe found in inventory or equipped. Please equip an axe before starting.");
            bot.core.ScriptManager.getInstance().stopScript();
            return;
        }

        logger.info("Woodcutting level: " + woodcuttingLevel);
        logger.info("Axe detected - ready to chop yews!");

        // Initialize metrics tracking
        metrics = new ScriptMetrics(WOODCUTTING_SKILL_ID);
        metrics.setStatus("Starting...");

        // Track starting inventory so we only count new logs
        startLogs = InventoryInteraction.getInventoryItemCount(YEW_LOGS_ID);

        logger.info("Statistics tracking initialized");

        // Start with a default state - updateStateIfNeeded will fix it
        currentState = State.CHOPPING;
    }

    @Override
    public boolean onLoop() throws InterruptedException {
        // Update log count (only new logs since start)
        int currentLogs = InventoryInteraction.getInventoryItemCount(YEW_LOGS_ID);
        int newLogs = currentLogs - startLogs;
        metrics.set("logs", Math.max(0, newLogs));

        // Update state if needed
        updateStateIfNeeded();

        // Update status based on current state
        switch (currentState) {
            case CHOPPING:
                metrics.setStatus("Chopping yew trees");
                return chopYewTree();

            case WALKING_TO_BANK:
                metrics.setStatus("Walking to bank");
                return walkToBank();

            case BANKING:
                metrics.setStatus("Banking logs");
                return handleBanking();

            case WALKING_TO_TREES:
                metrics.setStatus("Walking to trees");
                return walkToTrees();

            default:
                logger.error("Unknown state: " + currentState);
                return false;
        }
    }

    @Override
    public void onStop() {
        logger.info("Lumbridge Yew Chopper stopped");
    }

    @Override
    public java.util.Map<String, PaintMetric> getPaintMetrics() {
        if (metrics == null) {
            return new java.util.HashMap<>();
        }
        return metrics.generatePaintMetrics();
    }

    /**
     * Checks if the player has an axe in inventory or equipped
     */
    private boolean hasAxe() {
        for (int axeId : AXE_IDS) {
            if (InventoryInteraction.inventoryContains(axeId)) {
                return true;
            }
            // Check if axe is equipped
            if (EquipmentInteraction.isItemEquipped(axeId)) {
                return true;
            }
        }
        return false;
    }



    /**
     * Updates the state based on current conditions - checks conditions first, not current state
     */
    private void updateStateIfNeeded() {
        Tile playerLocation = Player.getLocation();

        // Always check conditions in priority order, regardless of current state

        // Priority 1: If inventory is full, we need to bank
        if (InventoryInteraction.isInventoryFull()) {
            if (playerLocation.distanceTo(DRAYNOR_BANK_LOCATION) < 10) {
                // We're at bank and inventory is full - bank
                if (currentState != State.BANKING) {
                    currentState = State.BANKING;
                    logger.info("At bank with full inventory - banking");
                }
            } else {
                // We're not at bank and inventory is full - walk to bank
                if (currentState != State.WALKING_TO_BANK) {
                    currentState = State.WALKING_TO_BANK;
                    logger.info("Inventory full - walking to bank");
                }
            }
            return;
        }

        // Priority 2: If inventory is not full, we should be chopping
        if (playerLocation.distanceTo(YEW_TREES_LOCATION) < 15) {
            // We're at trees and inventory not full - chop
            if (currentState != State.CHOPPING) {
                currentState = State.CHOPPING;
                logger.info("At yew trees with space - chopping");
            }
        } else {
            // We're not at trees and inventory not full - walk to trees
            if (currentState != State.WALKING_TO_TREES) {
                currentState = State.WALKING_TO_TREES;
                logger.info("Inventory has space - walking to trees");
            }
        }
    }

    /**
     * Chops a yew tree
     */
    private boolean chopYewTree() throws InterruptedException {
        // Check if already chopping - if so, wait for completion
        if (AnimationUtils.isAnimating()) {
            logger.info("Already chopping, waiting for completion");
            boolean animationCompleted = AnimationUtils.waitForAnimationToComplete(1800000); // 30 minutes timeout
            if (animationCompleted) {
                logger.info("Animation completed while already chopping");
            } else {
                logger.warning("Animation didn't complete within timeout, continuing anyway");
            }
            Thread.sleep(RandomUtils.random(500, 800));
            return true;
        }

        // Find the nearest yew tree
        Scenery yewTree = EntityUtils.findNearestScenery(YEW_TREE_IDS);

        if (yewTree == null) {
            logger.info("No yew trees found nearby, waiting...");
            Thread.sleep(2000);
            return true;
        }

        logger.info("Found yew tree, chopping...");

        // Use the SceneryInteractionBuilder to chop the tree
        boolean choppingStarted = new SceneryInteractionBuilder().with(YEW_TREE_IDS)
            .action("Chop down")
            .withDelay(300, 500)
            .execute();

        if (!choppingStarted) {
            logger.warning("Failed to start chopping yew tree");
            Thread.sleep(RandomUtils.random(1000, 1500));
            return true;
        }

        // Wait for the animation to start
        Thread.sleep(RandomUtils.random(1000, 1500));

        // Wait for the animation to complete (yews take a very long time)
        logger.info("Chopping in progress, waiting for completion");
        boolean animationCompleted = AnimationUtils.waitForAnimationToComplete(1800000); // 30 minutes
        if (animationCompleted) {
            logger.info("Chopping completed. Total yew logs gained: " + metrics.get("yew_logs"));
        } else {
            logger.warning("Animation didn't complete within timeout, continuing anyway");
        }

        // Add a small delay before next action
        Thread.sleep(RandomUtils.random(500, 800));
        return true;
    }

    /**
     * Walks to Draynor bank
     */
    private boolean walkToBank() throws InterruptedException {
        logger.info("Walking to Draynor bank");

        boolean walkResult = new WalkingBuilder()
            .to(DRAYNOR_BANK_LOCATION)
            .withRetries(2)
            .withDelay(300, 500)
            .execute();

        if (!walkResult) {
            logger.warning("Failed to walk to bank, retrying...");
            Thread.sleep(RandomUtils.random(2000, 3000));
            return true;
        }

        logger.info("Successfully walked to bank area");
        Thread.sleep(RandomUtils.random(500, 800));
        return true;
    }

    /**
     * Handles the banking process
     */
    private boolean handleBanking() throws InterruptedException {
        logger.info("Banking yew logs");

        // Find the nearest bank booth
        Scenery bankBooth = EntityUtils.findNearestScenery(BANK_BOOTH_ID);
        if (bankBooth == null) {
            logger.warning("Could not find bank booth");
            Thread.sleep(1000);
            return true;
        }

        // First, use SceneryInteractionBuilder to open the bank
        logger.info("Opening bank");
        boolean bankOpened = new SceneryInteractionBuilder()
            .with(BANK_BOOTH_ID)
            .action("Use-quickly")
            .withDelay(300, 500)
            .execute();

        if (!bankOpened) {
            logger.error("Failed to open bank");
            Thread.sleep(RandomUtils.random(1000, 1500));
            return true;
        }

        // Wait for the bank interface to open
        Thread.sleep(RandomUtils.random(1000, 1500));

        // Now use BankInteractionBuilder for banking operations with proper chaining
        logger.info("Depositing yew logs and keeping axe");
        boolean bankingSuccess = new BankInteractionBuilder()
            .depositAll()
            .except(AXE_IDS)
            .withDelay(300, 500)
            .execute();

        if (!bankingSuccess) {
            logger.error("Banking operations failed");
            Thread.sleep(RandomUtils.random(1000, 1500));
            return true;
        }

        metrics.increment("trips");
        // Reset start count since we banked
        startLogs = 0;
        logger.info("Banking completed successfully. Total trips: " + metrics.get("trips"));

        // Wait for the banking to complete
        Thread.sleep(RandomUtils.random(500, 800));

        return true;
    }

    /**
     * Walks to yew trees
     */
    private boolean walkToTrees() throws InterruptedException {
        logger.info("Walking to yew trees behind Lumbridge castle");

        boolean walkResult = new WalkingBuilder()
            .to(YEW_TREES_LOCATION)
            .withRetries(2)
            .withDelay(300, 500)
            .execute();

        if (!walkResult) {
            logger.warning("Failed to walk to yew trees, retrying...");
            Thread.sleep(RandomUtils.random(2000, 3000));
            return true;
        }

        logger.info("Successfully walked to yew trees");
        Thread.sleep(RandomUtils.random(500, 800));
        return true;
    }


}
