package bot.scripts;

import bot.api.interaction.InventoryInteraction;
import bot.api.builders.NPCInteractionBuilder;
import bot.api.builders.GroundItemInteractionBuilder;
import bot.util.game.AnimationUtils;
import bot.util.entity.EntityUtils;
import bot.util.game.entity.GroundItem;
import bot.util.RandomUtils;
import bot.util.game.entity.Npc;
import bot.api.script.BotScript;
import bot.api.script.ScriptCategory;
import bot.api.script.ScriptManifest;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.ui.paint.PaintProvider;
import bot.ui.paint.PaintMetric;
import bot.ui.paint.metrics.ScriptMetrics;
import bot.util.stats.XPTracker;

import java.util.List;

/**
 * A script that kills chickens in the Lumbridge chicken pen, collects feathers and bones,
 * and buries bones when no items are on the ground.
 */
@ScriptManifest(
        name = "Lumbridge Chicken Killer",
        version = 1.0,
        description = "Kills chickens in Lumbridge, collects feathers and bones, and buries bones",
        authors = {"Augment"},
        category = ScriptCategory.COMBAT,
        keywords = {"combat", "chickens", "lumbridge", "feathers", "bones"}
)
public class LumbridgeChickenKiller implements BotScript, PaintProvider {
    private static final Logger logger = LoggerFactory.getLogger(LumbridgeChickenKiller.class);

    // Chicken NPC ID - this will be replaced with correct ID
    private static final int CHICKEN_ID = 1; // Placeholder

    // Item IDs - these will be replaced with correct IDs
    private static final int FEATHER_ID = 314; // Placeholder
    private static final int BONES_ID = 526; // Placeholder

    // Skill IDs
    private static final int ATTACK_SKILL_ID = 0;
    private static final int STRENGTH_SKILL_ID = 2;
    private static final int DEFENCE_SKILL_ID = 1;
    private static final int HITPOINTS_SKILL_ID = 3;

    // Metrics tracking
    private ScriptMetrics metrics;

    @Override
    public void onStart() {
        logger.info("Lumbridge Chicken Killer started");

        // Initialize metrics tracking
        metrics = new ScriptMetrics(ATTACK_SKILL_ID, STRENGTH_SKILL_ID, DEFENCE_SKILL_ID, HITPOINTS_SKILL_ID);
        metrics.setStatus("Starting...");
    }

    @Override
    public boolean onLoop() throws InterruptedException {
        // Check if player is in combat
        if (AnimationUtils.isInCombat()) {
            metrics.setStatus("In combat");
            logger.info("Player is in combat, waiting...");
            Thread.sleep(RandomUtils.random(500, 1000));
            return true;
        }

        // Look for feathers on the ground
        GroundItem feathers = EntityUtils.findNearestGroundItem(FEATHER_ID);
        if (feathers != null) {
            metrics.setStatus("Collecting feathers");
            logger.info("Found feathers on the ground, picking up...");
            boolean feathersPickedUp = new GroundItemInteractionBuilder()
                .with(feathers)
                .action("Take")
                .withDelay(300, 500)
                .withMaxWalkDistance(15) // Use standardized method
                .execute();

            if (feathersPickedUp) {
                metrics.increment("items");
                Thread.sleep(RandomUtils.random(500, 800));
                return true;
            }


        // Look for bones on the ground
        GroundItem bones = EntityUtils.findNearestGroundItem(BONES_ID);
        if (bones != null) {
            metrics.setStatus("Collecting bones");
            logger.info("Found bones on the ground, picking up...");
            boolean bonesPickedUp = new GroundItemInteractionBuilder()
                .with(bones)
                .action("Take")
                .withDelay(300, 500)
                .withMaxWalkDistance(15) // Use standardized method
                .execute();

            if (bonesPickedUp) {
                metrics.increment("bones");
                logger.info("Successfully picked up bones");
                Thread.sleep(RandomUtils.random(500, 800));
                return true;
            }
        }

        // If no items on the ground, bury bones in inventory
        if (InventoryInteraction.inventoryContains(BONES_ID)) {
            metrics.setStatus("Burying bones");
            logger.info("Burying bones in inventory");
            boolean buriedBones = InventoryInteraction.interactWithInventoryItem(BONES_ID, "Bury");
            if (buriedBones) {
                metrics.increment("bones_buried");
                logger.info("Successfully buried bones");
                Thread.sleep(RandomUtils.random(500, 800));
                    return true;
                }
            }
        }

        // Attack a chicken if not in combat
        metrics.setStatus("Looking for chickens");
        return attackChicken();
    }





    /**
     * Attacks a chicken
     *
     * @return true if a chicken was successfully attacked
     * @throws InterruptedException if the thread is interrupted
     */
    private boolean attackChicken() throws InterruptedException {
        // Find the nearest chicken using EntityUtils (now returns bot.util.game.entity.Npc)
        Npc chicken = EntityUtils.findNearestNpc(CHICKEN_ID);

        if (chicken == null) {
            logger.info("No chickens found nearby, waiting...");
            Thread.sleep(RandomUtils.random(1000, 1500));
            return true;
        }

        logger.info("Found chicken, attacking...");
        metrics.setStatus("Attacking chicken");

        // Attack the chicken using the builder with standardized methods
        boolean attackStarted = new NPCInteractionBuilder()
            .with(chicken)
            .action("Attack")
            .withDelay(300, 500)
            .withMaxWalkDistance(10)
            .execute();

        if (!attackStarted) {
            logger.error("Failed to attack chicken");
            return false;
        }

        // Wait for the attack animation to start
        boolean animationStarted = AnimationUtils.waitForAnimationToStart();
        if (!animationStarted) {
            logger.error("Attack animation didn't start");
            return false;
        }

        // Track chicken kill
        metrics.increment("chickens_killed");

        // Wait for the chicken to die (animation to complete)

            boolean animationCompleted = AnimationUtils.waitForAnimationToComplete(10000);
       return true;
    }

    @Override
    public void onStop() {
        logger.info("Lumbridge Chicken Killer stopped");
    }

    @Override
    public java.util.Map<String, PaintMetric> getPaintMetrics() {
        if (metrics == null) {
            return new java.util.HashMap<>();
        }
        return metrics.generatePaintMetrics();
    }
}
