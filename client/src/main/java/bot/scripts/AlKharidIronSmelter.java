package bot.scripts;

import bot.api.interaction.InventoryInteraction;
import bot.api.interaction.WidgetInteraction;
import bot.api.builders.SceneryInteractionBuilder;
import bot.api.builders.WalkingBuilder;
import bot.util.game.AnimationUtils;
import bot.util.entity.EntityUtils;
import bot.util.entity.ScreenUtils;
import bot.util.RandomUtils;

import bot.util.game.entity.Player;
import bot.util.game.world.Tile;
import bot.api.script.BotScript;
import bot.api.script.ScriptCategory;
import bot.api.script.ScriptManifest;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.ui.paint.PaintProvider;
import bot.ui.paint.PaintMetric;
import bot.ui.paint.metrics.ScriptMetrics;
import bot.util.stats.XPTracker;

import java.util.List;
import rt4.Scenery;

/**
 * A script that smelts iron ore into iron bars at Al Kharid furnace and banks them.
 * Can be started from either the bank or the furnace.
 */
@ScriptManifest(
        name = "Al Kharid Iron Smelter",
        version = 1.0,
        description = "Smelts iron ore into iron bars at Al Kharid furnace and banks them",
        authors = {"Augment"},
        category = ScriptCategory.SKILLING,
        keywords = {"smithing", "iron", "al kharid", "banking", "smelting"}
)
public class AlKharidIronSmelter implements BotScript, PaintProvider {
    private static final Logger logger = LoggerFactory.getLogger(AlKharidIronSmelter.class);

    // Script states
    private enum State {
        SMELTING,
        WALKING_TO_BANK,
        BANKING,
        WALKING_TO_FURNACE
    }

    // Furnace ID - this will be replaced with correct ID
    private static final int FURNACE_ID = 11666; // Placeholder

    // Bank booth ID - this will be replaced with correct ID
    private static final int BANK_BOOTH_ID = 11758; // Placeholder

    // Iron ore ID - this will be replaced with correct ID
    private static final int IRON_ORE_ID = 440; // Placeholder

    // Iron bar ID - this will be replaced with correct ID
    private static final int IRON_BAR_ID = 2351; // Placeholder

    // Smelting interface component ID for iron
    private static final int SMELTING_IRON_COMPONENT = WidgetInteraction.Components.SMELTING_IRON;

    // Skill ID
    private static final int SMITHING_SKILL_ID = 13;

    // Path from furnace to bank
    private final Tile[] pathToBank = {
            new Tile(3275, 3186, 0), // Furnace
            new Tile(3276, 3180, 0),
            new Tile(3276, 3174, 0),
            new Tile(3276, 3168, 0),
            new Tile(3276, 3162, 0),
            new Tile(3276, 3156, 0),
            new Tile(3276, 3150, 0),
            new Tile(3276, 3144, 0),
            new Tile(3276, 3138, 0),
            new Tile(3276, 3132, 0),
            new Tile(3276, 3126, 0),
            new Tile(3276, 3120, 0),
            new Tile(3270, 3120, 0), // Bank
    };

    // Location constants
    private static final Tile FURNACE_LOCATION = new Tile(3275, 3186, 0); // Al Kharid furnace
    private static final Tile BANK_LOCATION = new Tile(3270, 3120, 0); // Al Kharid bank

    // Distance thresholds
    private static final int NEAR_DISTANCE = 10; // Distance in tiles to consider "near" a location

    // Current state of the script
    private State currentState;

    // Time tracking for animation timeout
    private long lastAnimationTime = 0;

    // Metrics tracking
    private ScriptMetrics metrics;
    private int startBars;



    @Override
    public void onStart() {
        logger.info("Al Kharid Iron Smelter started");

        // Initialize metrics tracking
        metrics = new ScriptMetrics(SMITHING_SKILL_ID);
        metrics.setStatus("Starting...");

        // Start with a default state - updateStateIfNeeded will fix it
        currentState = State.SMELTING;
        logger.info("Starting in state: " + currentState);
    }

    @Override
    public boolean onLoop() throws InterruptedException {
        // Check if we need to update the state based on current conditions
        updateStateIfNeeded();

        // Update status based on current state
        switch (currentState) {
            case SMELTING:
                metrics.setStatus("Smelting iron bars");
                return smeltIron();

            case WALKING_TO_BANK:
                metrics.setStatus("Walking to bank");
                logger.info("Walking to bank");
                boolean reachedBank = walkToBank();

                if (reachedBank) {
                    logger.info("Reached bank");
                    currentState = State.BANKING;
                }

                return true;

            case BANKING:
                metrics.setStatus("Banking items");
                logger.info("Banking items");
                boolean bankingComplete = bankItems();

                if (bankingComplete) {
                    logger.info("Banking complete, walking back to furnace");
                    currentState = State.WALKING_TO_FURNACE;
                }

                return true;

            case WALKING_TO_FURNACE:
                // Walk back to furnace
                logger.info("Walking back to furnace");
                boolean reachedFurnace = walkToFurnace();

                if (reachedFurnace) {
                    logger.info("Reached furnace");
                    currentState = State.SMELTING;
                }

                return true;

            default:
                logger.error("Unknown state: " + currentState);
                return false;
        }
    }





    /**
     * Checks if the player is near the bank
     *
     * @param playerLocation The player's current location
     * @return true if the player is near the bank
     */
    private boolean isNearBank(Tile playerLocation) {
        return playerLocation.distanceTo(BANK_LOCATION) < NEAR_DISTANCE;
    }

    /**
     * Checks if the player is near the furnace
     *
     * @param playerLocation The player's current location
     * @return true if the player is near the furnace
     */
    private boolean isNearFurnace(Tile playerLocation) {
        return playerLocation.distanceTo(FURNACE_LOCATION) < NEAR_DISTANCE;
    }

    /**
     * Updates the state if needed based on current conditions
     * This helps handle situations where the player might be moved by random events
     * or other factors during script execution
     */
    private void updateStateIfNeeded() {
        // Get the player's current location
        Tile playerLocation = Player.getLocation();

        // Always check conditions in priority order, regardless of current state

        // Priority 1: If we need to bank (no materials or inventory full), we need to bank
        if (shouldGoToBank()) {
            if (isNearBank(playerLocation)) {
                // We're at bank and need to bank - bank
                if (currentState != State.BANKING) {
                    currentState = State.BANKING;
                    logger.info("At bank and need to bank - banking");
                }
            } else {
                // We're not at bank and need to bank - walk to bank
                if (currentState != State.WALKING_TO_BANK) {
                    currentState = State.WALKING_TO_BANK;
                    logger.info("Need to bank - walking to bank");
                }
            }
            return;
        }

        // Priority 2: If we have materials, we should be smelting
        if (isNearFurnace(playerLocation)) {
            // We're at furnace and have materials - smelt
            if (currentState != State.SMELTING) {
                currentState = State.SMELTING;
                logger.info("At furnace with materials - smelting");
            }
        } else {
            // We're not at furnace and have materials - walk to furnace
            if (currentState != State.WALKING_TO_FURNACE) {
                currentState = State.WALKING_TO_FURNACE;
                logger.info("Have materials - walking to furnace");
            }
        }
    }

    /**
     * Checks if we should go to the bank
     * We should go to the bank if:
     * 1. We have no iron ore left, or
     * 2. We have iron bars and have been idle for more than 5 seconds
     *
     * @return true if we should go to the bank
     */
    private boolean shouldGoToBank() {
        // Check if we have no iron ore left
        if (!hasIronOre()) {
            logger.info("No iron ore left, need to bank");
            return true;
        }

        // Check if we have iron bars and have been idle for more than 5 seconds
        if (hasIronBars() && AnimationUtils.getAnimation() == -1) {
            long currentTime = System.currentTimeMillis();

            // Initialize last animation time if it's 0
            if (lastAnimationTime == 0) {
                lastAnimationTime = currentTime;
                return false;
            }

            // Check if we've been idle for more than 5 seconds
            if (currentTime - lastAnimationTime > 5000) {
                logger.info("Idle for more than 5 seconds with iron bars, need to bank");
                return true;
            }
        } else if (AnimationUtils.getAnimation() != -1) {
            // Update last animation time if we're animating
            lastAnimationTime = System.currentTimeMillis();
        }

        return false;
    }

    /**
     * Smelts iron ore into iron bars
     *
     * @return true if the smelting action was successful
     * @throws InterruptedException if the thread is interrupted
     */
    private boolean smeltIron() throws InterruptedException {
        // Check if we have iron ore
        if (!hasIronOre()) {
            logger.info("No iron ore to smelt");
            return true;
        }

        // Find the furnace
        Scenery furnace = EntityUtils.findNearestScenery(FURNACE_ID);

        if (furnace == null) {
            logger.info("No furnace found nearby, waiting...");
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                logger.error("Interrupted while waiting for furnace", e);
                return false;
            }
            return true;
        }

        logger.info("Found furnace, smelting...");

        // Check if furnace is on screen
        boolean furnaceOnScreen = ScreenUtils.isSceneryOnScreen(furnace);

        // Check if the smelting interface is already open
        if (WidgetInteraction.isInterfaceOpen(WidgetInteraction.Components.SMELTING >> 16)) {
            logger.info("Smelting interface already open, selecting iron");
            // Click on the iron option
            boolean selected = WidgetInteraction.sendSpecialAction(SMELTING_IRON_COMPONENT);

            if (!selected) {
                logger.error("Failed to select iron in smelting interface");
                return false;
            }

            // Track iron bars gained
            int currentBars = InventoryInteraction.getInventoryItemCount(IRON_BAR_ID);
            int previousBars = metrics.get("iron_bars_in_inventory");
            if (currentBars > previousBars) {
                int gained = currentBars - previousBars;
                metrics.increment("iron_bars", gained);
                metrics.set("iron_bars_in_inventory", currentBars);
            }

            // Wait for the animation to start
            if (AnimationUtils.getAnimation() == -1) {
                logger.info("Waiting for smelting animation to start");

                    boolean animationStarted = AnimationUtils.waitForAnimationToStart();
                    if (!animationStarted) {
                        logger.error("Smelting animation didn't start");
                        return false;
                    }


                // Update last animation time
                lastAnimationTime = System.currentTimeMillis();
            }

            return true;
        }

        // Interact with the furnace
        boolean smeltingStarted = false;
        // Use the SceneryInteractionBuilder for better handling of walking and interaction

            smeltingStarted = new SceneryInteractionBuilder().with(FURNACE_ID) // Use the builder pattern
                .action("Smelt")
                .withDelay(300, 500)
                .withMaxWalkDistance(15) // Use standardized method
                .execute();

            if (!smeltingStarted) {
                logger.error("Failed to start smelting");
                return false;
            }

        // Wait for the smelting interface to open
        Thread.sleep(RandomUtils.random(1000, 1500));
        // Let InterruptedException propagate to properly handle script pausing/stopping

        // Check if the smelting interface opened
        if (!WidgetInteraction.isInterfaceOpen(WidgetInteraction.Components.SMELTING >> 16)) {
            logger.error("Smelting interface didn't open");
            return false;
        }

        // Click on the iron option
        logger.info("Selecting iron in smelting interface");
        boolean selected = WidgetInteraction.sendSpecialAction(SMELTING_IRON_COMPONENT);

        if (!selected) {
            logger.error("Failed to select iron in smelting interface");
            return false;
        }

        // Track iron bars gained
        int currentBars = InventoryInteraction.getInventoryItemCount(IRON_BAR_ID);
        int previousBars = metrics.get("iron_bars_in_inventory");
        if (currentBars > previousBars) {
            int gained = currentBars - previousBars;
            metrics.increment("iron_bars", gained);
            metrics.set("iron_bars_in_inventory", currentBars);
        }

        // Wait for the animation to start
        if (AnimationUtils.getAnimation() == -1) {
            logger.info("Waiting for smelting animation to start");

                boolean animationStarted = AnimationUtils.waitForAnimationToStart();
                if (!animationStarted) {
                    logger.error("Smelting animation didn't start");
                    return false;
                }

                // Update last animation time
                lastAnimationTime = System.currentTimeMillis();

        }

        return true;
    }

    /**
     * Walks to the bank
     *
     * @return true if the player reached the bank
     * @throws InterruptedException if the thread is interrupted
     */
    private boolean walkToBank() throws InterruptedException {
        // Use the WalkingBuilder to walk to the bank

            return new WalkingBuilder()
                    .alongPath(pathToBank)
                    .withDelay(500, 800)

                    .execute();

    }

    /**
     * Banks items - deposits iron bars and withdraws iron ore
     *
     * @return true if banking was successful
     * @throws InterruptedException if the thread is interrupted
     */
    private boolean bankItems() throws InterruptedException {
        // Find the nearest bank booth
        Scenery bankBooth = EntityUtils.findNearestScenery(BANK_BOOTH_ID);

        if (bankBooth == null) {
            logger.error("No bank booth found nearby");
            return false;
        }

        // First, use SceneryInteractionBuilder to open the bank
        logger.info("Using SceneryInteractionBuilder to open the bank");

            boolean bankOpened = new SceneryInteractionBuilder().with(BANK_BOOTH_ID) // Use the builder pattern
                .action("Use-quickly")
                .withDelay(300, 500)
                .execute();

            if (!bankOpened) {
                logger.error("Failed to open bank");
                return false;
            }


        // Wait for the bank interface to open
        Thread.sleep(RandomUtils.random(1000, 1500));
        // Let InterruptedException propagate to properly handle script pausing/stopping

        // Now use BankInteractionBuilder for banking operations
        logger.info("Using BankInteractionBuilder for banking operations");
        bot.api.builders.BankInteractionBuilder bankBuilder = new bot.api.builders.BankInteractionBuilder()
            .withDelay(300, 500);

        // Configure the banking operations
        bankBuilder.depositAll() // Deposit all items
                  .withdraw(IRON_ORE_ID, 28) // Withdraw 28 iron ore
                  .withDelay(500, 800); // Add a delay between operations

        // Execute the banking operations
        bankBuilder.execute();




        // Wait for the bank interface to close (the builder should have closed it)
        try {
            Thread.sleep(RandomUtils.random(500, 800));
        } catch (InterruptedException e) {
            logger.error("Interrupted while waiting for bank to close", e);
            return false;
        }

        metrics.increment("banking_trips");
        // Reset inventory tracking since we banked
        metrics.set("iron_bars_in_inventory", 0);
        return true;
    }

    /**
     * Walks back to the furnace
     *
     * @return true if the player reached the furnace
     * @throws InterruptedException if the thread is interrupted
     */
    private boolean walkToFurnace() throws InterruptedException {
        // Create a reversed path to walk back to the furnace
        Tile[] pathToFurnace = new Tile[pathToBank.length];

        for (int i = 0; i < pathToBank.length; i++) {
            pathToFurnace[i] = pathToBank[pathToBank.length - 1 - i];
        }

        // Use the WalkingBuilder to walk back to the furnace

            return new WalkingBuilder()
                    .alongPath(pathToFurnace)
                    .withDelay(500, 800)

                    .execute();

    }

    /**
     * Checks if the inventory contains iron ore
     *
     * @return true if the inventory contains iron ore
     */
    private boolean hasIronOre() {
        return InventoryInteraction.inventoryContains(IRON_ORE_ID);
    }

    /**
     * Checks if the inventory contains iron bars
     *
     * @return true if the inventory contains iron bars
     */
    private boolean hasIronBars() {
        return InventoryInteraction.inventoryContains(IRON_BAR_ID);
    }

    @Override
    public void onStop() {
        logger.info("Al Kharid Iron Smelter stopped");
    }

    @Override
    public java.util.Map<String, PaintMetric> getPaintMetrics() {
        if (metrics == null) {
            return new java.util.HashMap<>();
        }
        return metrics.generatePaintMetrics();
    }
}
