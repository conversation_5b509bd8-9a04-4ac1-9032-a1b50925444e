package bot.scripts;

import bot.api.interaction.InventoryInteraction;
import bot.api.interaction.WidgetInteraction;
import bot.api.builders.BankInteractionBuilder;
import bot.api.builders.SceneryInteractionBuilder;
import bot.api.builders.WalkingBuilder;
import bot.api.builders.WidgetInteractionBuilder;
import bot.util.entity.EntityUtils;
import bot.util.game.entity.Item;
import bot.util.game.entity.Player;
import bot.util.game.world.Tile;
import bot.util.RandomUtils;
import bot.api.script.BotScript;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.ui.paint.PaintProvider;
import bot.ui.paint.PaintMetric;
import bot.ui.paint.metrics.ScriptMetrics;
import bot.util.stats.XPTracker;

import java.util.List;
import rt4.Component;
import rt4.InterfaceList;
import rt4.Scenery;

/**
 * A script that smiths bronze platebodies at West Varrock anvil.
 * Can start from either the bank or the anvil.
 * Each platebody requires 5 bronze bars.
 */
public class WestVarrockBronzeSmither implements BotScript, PaintProvider {
    private static final Logger logger = LoggerFactory.getLogger(WestVarrockBronzeSmither.class);

    // Constants
    private static final int BANK_BOOTH_ID = 11402;
    private static final int ANVIL_ID = 2783;
    private static final int BRONZE_BAR_ID = 2349;
    private static final int BRONZE_PLATEBODY_ID = 2399;

    // Smithing interface constants
    private static final int SMITHING_INTERFACE_ID = 312;
    private static final int PLATEBODY_COMPONENT_ID = 16; // Component ID for platebody in the smithing interface

    // Skill ID
    private static final int SMITHING_SKILL_ID = 13;

    // Locations
    private static final Tile BANK_LOCATION = new Tile(3185, 3436, 0); // West Varrock bank
    private static final Tile ANVIL_LOCATION = new Tile(3188, 3426, 0); // West Varrock anvil

    // Path from bank to anvil
    private final Tile[] pathToAnvil = {
        new Tile(3185, 3436, 0), // Bank
        new Tile(3185, 3433, 0),
        new Tile(3186, 3430, 0),
        new Tile(3188, 3426, 0)  // Anvil
    };

    // Path from anvil to bank
    private final Tile[] pathToBank = {
        new Tile(3188, 3426, 0), // Anvil
        new Tile(3186, 3430, 0),
        new Tile(3185, 3433, 0),
        new Tile(3185, 3436, 0)  // Bank
    };

    // Script state
    private enum State {
        BANKING,
        WALKING_TO_ANVIL,
        SMITHING,
        WALKING_TO_BANK,
        ERROR
    }

    private State currentState;

    // Metrics tracking
    private ScriptMetrics metrics;
    private int startItems;

    @Override
    public void onStart() {
        logger.info("Starting West Varrock Bronze Platebody Smither");

        // Initialize metrics tracking
        metrics = new ScriptMetrics(SMITHING_SKILL_ID);
        metrics.setStatus("Starting...");

        // Start with a default state - updateStateIfNeeded will fix it
        currentState = State.SMITHING;
    }

    @Override
    public boolean onLoop() throws InterruptedException {
        // Check if we need to update the state
        updateStateIfNeeded();

        // Execute the current state and update status
        switch (currentState) {
            case BANKING:
                metrics.setStatus("Banking items");
                handleBanking();
                break;
            case WALKING_TO_ANVIL:
                metrics.setStatus("Walking to anvil");
                walkToAnvil();
                break;
            case SMITHING:
                metrics.setStatus("Smithing platebodies");
                handleSmithing();
                break;
            case WALKING_TO_BANK:
                metrics.setStatus("Walking to bank");
                walkToBank();
                break;
            case ERROR:
                metrics.setStatus("Error state");
                logger.error("Script is in error state. Please restart.");
                return false; // Wait 5 seconds before trying again
        }

        return true; // Small delay between loops
    }



    /**
     * Updates the state if needed based on current conditions
     */
    private void updateStateIfNeeded() {
        Tile playerLocation = Player.getLocation();
        int bronzeBars = countBronzeBars();

        // Always check conditions in priority order, regardless of current state

        // Priority 1: If we don't have enough bars, we need to bank
        if (bronzeBars < 5) {
            if (playerLocation.distanceTo(BANK_LOCATION) < 3) {
                // We're at bank and need bars - bank
                if (currentState != State.BANKING) {
                    currentState = State.BANKING;
                    logger.info("At bank and need bars - banking");
                }
            } else {
                // We're not at bank and need bars - walk to bank
                if (currentState != State.WALKING_TO_BANK) {
                    currentState = State.WALKING_TO_BANK;
                    logger.info("Need bars - walking to bank");
                }
            }
            return;
        }

        // Priority 2: If we have enough bars, we should be smithing
        if (playerLocation.distanceTo(ANVIL_LOCATION) < 3) {
            // We're at anvil and have bars - smith
            if (currentState != State.SMITHING) {
                currentState = State.SMITHING;
                logger.info("At anvil with bars - smithing");
            }
        } else {
            // We're not at anvil and have bars - walk to anvil
            if (currentState != State.WALKING_TO_ANVIL) {
                currentState = State.WALKING_TO_ANVIL;
                logger.info("Have bars - walking to anvil");
            }
        }
    }

    /**
     * Handles the banking process
     */
    private void handleBanking() throws InterruptedException {
        logger.info("Banking for bronze bars");

        // Find the nearest bank booth
        Scenery bankBooth = EntityUtils.findNearestScenery(BANK_BOOTH_ID);
        if (bankBooth == null) {
            logger.warning("Could not find bank booth");
            return;
        }

        // First, use SceneryInteractionBuilder to open the bank
        logger.info("Opening bank");
        boolean bankOpened = new SceneryInteractionBuilder().with(BANK_BOOTH_ID)
            .action("Use-quickly")
            .withDelay(300, 500)
            .execute();

        if (!bankOpened) {
            logger.error("Failed to open bank");
            return;
        }

        // Wait for the bank interface to open
        Thread.sleep(RandomUtils.random(1000, 1500));

        // Now use BankInteractionBuilder for banking operations
        logger.info("Depositing items and withdrawing bronze bars");
        BankInteractionBuilder bankBuilder = new BankInteractionBuilder()
            .withDelay(300, 500);

        // Deposit all items except bronze bars
        bankBuilder.depositAll();

        // Withdraw 28 bronze bars
        bankBuilder.withdraw(BRONZE_BAR_ID, 28);

        // Execute the banking operations
        boolean bankingSuccess = bankBuilder.execute();

        if (!bankingSuccess) {
            logger.error("Banking operations failed");
            return;
        }



        // Wait for the deposit to complete
        Thread.sleep(RandomUtils.random(500, 800));

        // Track banking trip
        metrics.increment("banking_trips");
        // Reset inventory tracking since we banked
        metrics.set("bronze_platebodies_in_inventory", 0);

        // Update state to walking to anvil
        currentState = State.WALKING_TO_ANVIL;

    }

    /**
     * Walks to the anvil
     */
    private void walkToAnvil() throws InterruptedException {
        Tile playerLocation = Player.getLocation();

        // Check if we're already at the anvil
        if (playerLocation.distanceTo(ANVIL_LOCATION) < 3) {
            // We're already at the anvil, update state
            currentState = State.SMITHING;
            logger.info("Already at anvil. Starting smithing.");
            return;
        }

        logger.info("Walking to anvil");

        // Use WalkingBuilder to walk to the anvil along a predefined path
        boolean walkResult = new WalkingBuilder()
            .alongPath(pathToAnvil)
            .withDelay(500, 800)
            .execute();

        if (!walkResult) {
            logger.warning("Failed to walk to anvil");
        }
    }

    /**
     * Handles the smithing process
     */
    private void handleSmithing() throws InterruptedException {
        logger.info("Smithing bronze platebodies");

        // Check if we have enough bronze bars
        if (countBronzeBars() < 5) {
            logger.info("Not enough bronze bars to smith a platebody");
            currentState = State.WALKING_TO_BANK;
            return;
        }

        // Find the nearest anvil
        Scenery anvil = EntityUtils.findNearestScenery(ANVIL_ID);
        if (anvil == null) {
            logger.warning("Could not find anvil");
            return;
        }

        // Check if the smithing interface is already open
        if (!isSmithingInterfaceOpen()) {
            // Use SceneryInteractionBuilder to interact with the anvil
            logger.info("Interacting with anvil");
            boolean anvilInteracted = new SceneryInteractionBuilder().with(ANVIL_ID)
                .action("Smith")
                .withDelay(300, 500)
                .execute();

            if (!anvilInteracted) {
                logger.error("Failed to interact with anvil");
                return;
            }

            // Wait for the smithing interface to open
            Thread.sleep(RandomUtils.random(1000, 1500));

            // Check if the smithing interface opened
            if (!isSmithingInterfaceOpen()) {
                logger.error("Smithing interface did not open");
                return;
            }
        }

        // Get the platebody component from the smithing interface
        Component platebodyComponent = InterfaceList.getComponent(SMITHING_INTERFACE_ID << 16 | PLATEBODY_COMPONENT_ID);
        if (platebodyComponent == null) {
            logger.error("Could not find platebody component in smithing interface");
            return;
        }

        // Use WidgetInteractionBuilder to select the platebody
        logger.info("Selecting bronze platebody");
        boolean platebodySelected = new WidgetInteractionBuilder()
            .withHash(SMITHING_INTERFACE_ID << 16 | PLATEBODY_COMPONENT_ID)
            .withAction(1)
            .withDelay(300, 500)
            .execute();

        if (!platebodySelected) {
            logger.error("Failed to select bronze platebody");
            return;
        }

        // Wait for the smithing to complete (animation to finish)
        logger.info("Waiting for smithing to complete");
        Thread.sleep(RandomUtils.random(3000, 4000)); // Smithing takes a few seconds

        // Track bronze platebodies gained
        int currentPlatebodies = InventoryInteraction.getInventoryItemCount(BRONZE_PLATEBODY_ID);
        int previousPlatebodies = metrics.get("bronze_platebodies_in_inventory");
        if (currentPlatebodies > previousPlatebodies) {
            int gained = currentPlatebodies - previousPlatebodies;
            metrics.increment("bronze_platebodies", gained);
            metrics.set("bronze_platebodies_in_inventory", currentPlatebodies);
        }
        logger.info("Smithed bronze platebody. Total: " + metrics.get("bronze_platebodies"));

        // Check if we need to continue smithing or go to bank
        if (countBronzeBars() < 5) {
            currentState = State.WALKING_TO_BANK;
        }

    }

    /**
     * Walks to the bank
     */
    private void walkToBank() throws InterruptedException {
        Tile playerLocation = Player.getLocation();

        // Check if we're already at the bank
        if (playerLocation.distanceTo(BANK_LOCATION) < 3) {
            // We're already at the bank, update state
            currentState = State.BANKING;
            logger.info("Already at bank. Starting banking.");
            return;
        }

        logger.info("Walking to bank");

        // Use WalkingBuilder to walk to the bank along a predefined path
        boolean walkResult = new WalkingBuilder()
            .alongPath(pathToBank)
            .withDelay(500, 800)
            .execute();

        if (!walkResult) {
            logger.warning("Failed to walk to bank");
        }
    }

    /**
     * Counts the number of bronze bars in the inventory
     */
    private int countBronzeBars() {
        int count = 0;

        for (int i = 0; i < 28; i++) {
            Item item = InventoryInteraction.getItem(i);
            if (item != null && item.getId() == BRONZE_BAR_ID) {
                count++;
            }
        }

        return count;
    }

    /**
     * Checks if the smithing interface is open
     */
    private boolean isSmithingInterfaceOpen() {
        return WidgetInteraction.isInterfaceOpen(SMITHING_INTERFACE_ID);
    }

    @Override
    public void onStop() {
        logger.info("Stopping West Varrock Bronze Platebody Smither");
    }

    @Override
    public java.util.Map<String, PaintMetric> getPaintMetrics() {
        if (metrics == null) {
            return new java.util.HashMap<>();
        }
        return metrics.generatePaintMetrics();
    }
}
