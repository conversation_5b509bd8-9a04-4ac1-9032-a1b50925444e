package bot.scripts;

import bot.api.interaction.InventoryInteraction;
import bot.api.builders.SceneryInteractionBuilder;
import bot.util.game.AnimationUtils;
import bot.util.entity.EntityUtils;
import bot.util.game.world.Tile;
import bot.util.game.entity.Player;
import bot.util.RandomUtils;
import bot.api.script.BotScript;
import bot.api.script.ScriptCategory;
import bot.api.script.ScriptManifest;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.ui.paint.PaintProvider;
import bot.ui.paint.PaintMetric;
import bot.ui.paint.metrics.ScriptMetrics;
import bot.util.stats.XPTracker;

import java.util.List;
import rt4.Scenery;

/**
 * A powerchopper script that chops trees based on woodcutting level:
 * - Level 1-14: Normal trees
 * - Level 15-29: Oak trees  
 * - Level 30+: Willow trees
 * Fills inventory then drops all logs. Automatically walks to trees when not on screen.
 */
@ScriptManifest(
        name = "Power Chopper",
        version = 1.0,
        description = "Chops trees based on level, fills inventory and drops logs",
        authors = {"Augment"},
        category = ScriptCategory.SKILLING,
        keywords = {"woodcutting", "chopping", "trees", "power", "dropping"}
)
public class PowerChopper implements BotScript, PaintProvider {
    private static final Logger logger = LoggerFactory.getLogger(PowerChopper.class);

    // Script states
    private enum State {
        CHOPPING,
        DROPPING
    }

    // Skill ID for woodcutting
    private static final int WOODCUTTING_SKILL_ID = 8;

    // Tree object IDs
    private static final int[] NORMAL_TREE_ID = {1276, 1278};
    private static final int[] OAK_TREE_ID = {1281};
    private static final int[] WILLOW_TREE_ID = {5553, 5552};

    // Log item IDs
    private static final int NORMAL_LOGS_ID = 1512;
    private static final int OAK_LOGS_ID = 1522;
    private static final int WILLOW_LOGS_ID = 1520;

    // Axe item IDs for checking if player has an axe
    private static final int[] AXE_IDS = {1352, 1350, 1354, 1356, 1358, 1360}; // Bronze to Rune

    // Tree locations for different types (these are example locations - should be updated for specific areas)
    private static final Tile[] NORMAL_TREE_LOCATIONS = {
        new Tile(3164, 3288, 0), // Lumbridge
        new Tile(3165, 3289, 0),
        new Tile(3166, 3290, 0)
    };

    private static final Tile[] OAK_TREE_LOCATIONS = {
        new Tile(3166, 3419, 0), // Varrock West Bank area
        new Tile(3167, 3420, 0),
        new Tile(3168, 3421, 0)
    };

    private static final Tile[] WILLOW_TREE_LOCATIONS = {
        new Tile(3086, 3234, 0), // Draynor Village
        new Tile(3087, 3235, 0),
        new Tile(3088, 3236, 0)
    };

    // Current state and tree info
    private State currentState = State.CHOPPING;
    private int[] currentTreeId;
    private int currentLogId;
    private Tile[] currentTreeLocations;

    // Metrics tracking
    private ScriptMetrics metrics;



    @Override
    public void onStart() {
        logger.info("Power Chopper started");

        // Check if player has an axe
        if (!hasAxe()) {
            logger.error("No axe found in inventory. Please equip an axe before starting.");
            bot.core.ScriptManager.getInstance().stopScript();
            return;
        }


        // Determine which tree to chop based on woodcutting level
        determineTreeType();

        logger.info("Woodcutting level: " + Player.getBaseLevel(WOODCUTTING_SKILL_ID));
        logger.info("Target tree type: " + getTreeName());

        // Initialize metrics tracking
        metrics = new ScriptMetrics(WOODCUTTING_SKILL_ID);
        metrics.setStatus("Starting...");

        // Start with a default state - updateStateIfNeeded will fix it
        currentState = State.CHOPPING;
    }

    @Override
    public boolean onLoop() throws InterruptedException {
        // Update state if needed
        updateStateIfNeeded();

        // Update status based on current state
        switch (currentState) {
            case CHOPPING:
                metrics.setStatus("Chopping " + getTreeName().toLowerCase());
                return chopTree();

            case DROPPING:
                metrics.setStatus("Dropping logs");
                return dropLogs();

            default:
                logger.error("Unknown state: " + currentState);
                return false;
        }
    }



    /**
     * Determines which tree type to chop based on woodcutting level
     */
    private void determineTreeType() {
        int woodcuttingLevel = Player.getBaseLevel(WOODCUTTING_SKILL_ID);

        if (woodcuttingLevel >= 30) {
            // Willow trees
            currentTreeId = WILLOW_TREE_ID;
            currentLogId = WILLOW_LOGS_ID;
            currentTreeLocations = WILLOW_TREE_LOCATIONS;
        } else if (woodcuttingLevel >= 15) {
            // Oak trees
            currentTreeId = OAK_TREE_ID;
            currentLogId = OAK_LOGS_ID;
            currentTreeLocations = OAK_TREE_LOCATIONS;
        } else {
            // Normal trees
            currentTreeId = NORMAL_TREE_ID;
            currentLogId = NORMAL_LOGS_ID;
            currentTreeLocations = NORMAL_TREE_LOCATIONS;
        }
    }

    /**
     * Gets the name of the current tree type for logging
     */
    private String getTreeName() {
        if (currentTreeId == WILLOW_TREE_ID) return "Willow";
        if (currentTreeId == OAK_TREE_ID) return "Oak";
        return "Normal";
    }



    /**
     * Updates the state based on current conditions
     */
    private void updateStateIfNeeded() {
        // Always check conditions in priority order, regardless of current state

        // Priority 1: If inventory is full, we need to drop
        if (InventoryInteraction.isInventoryFull()) {
            if (currentState != State.DROPPING) {
                currentState = State.DROPPING;
                logger.info("Inventory full - dropping logs");
            }
            return;
        }

        // Priority 2: If inventory is not full, we should be chopping
        if (currentState != State.CHOPPING) {
            currentState = State.CHOPPING;
            logger.info("Inventory has space - chopping");
        }
    }

    /**
     * Checks if the player has an axe in their inventory
     */
    private boolean hasAxe() {
        for (int axeId : AXE_IDS) {
            if (InventoryInteraction.inventoryContains(axeId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if the player has logs in their inventory
     */
    private boolean hasLogsInInventory() {
        return InventoryInteraction.inventoryContains(NORMAL_LOGS_ID) ||
               InventoryInteraction.inventoryContains(OAK_LOGS_ID) ||
               InventoryInteraction.inventoryContains(WILLOW_LOGS_ID);
    }

    /**
     * Chops a tree
     */
    private boolean chopTree() throws InterruptedException {
        // Check if already chopping - if so, wait for completion
        if (AnimationUtils.isAnimating()) {
            logger.info("Already chopping, waiting for completion");
            boolean animationCompleted = AnimationUtils.waitForAnimationToComplete(10000);
            Thread.sleep(RandomUtils.random(500, 800));
            return true;
        }

        // Find the nearest tree of the current type
        Scenery tree = EntityUtils.findNearestScenery(currentTreeId);

        if (tree == null) {
            logger.info("No " + getTreeName().toLowerCase() + " trees found nearby, waiting...");
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                logger.error("Interrupted while waiting for trees", e);
                return false;
            }
            return true;
        }

        logger.info("Found " + getTreeName().toLowerCase() + " tree, chopping...");

        // Use the SceneryInteractionBuilder to chop the tree
        // This will handle walking to the tree and interacting with it
        boolean choppingStarted = new SceneryInteractionBuilder().with(currentTreeId)
            .action("Chop down")
            .withDelay(300, 500)
            .execute();

        if (!choppingStarted) {
            logger.warning("Failed to start chopping, will try again next loop");
            return true; // Continue script, don't stop
        }

        // Wait for the animation to start
        logger.info("Waiting for chopping animation to start");
        boolean animationStarted = AnimationUtils.waitForAnimationToStart();
        if (!animationStarted) {
            logger.warning("Chopping animation didn't start within timeout, continuing anyway");
            return true;
        }

        // Wait for the animation to complete
        logger.info("Chopping in progress, waiting for completion");
        boolean animationCompleted = AnimationUtils.waitForAnimationToComplete(10000);
        // Add a small delay before next action
        Thread.sleep(RandomUtils.random(500, 800));
        return true;
    }

    /**
     * Drops all logs from inventory
     */
    private boolean dropLogs() throws InterruptedException {
        logger.info("Dropping logs from inventory");

        // Drop normal logs
        if (InventoryInteraction.inventoryContains(NORMAL_LOGS_ID)) {
            boolean dropped = InventoryInteraction.interactWithInventoryItem(NORMAL_LOGS_ID, "Drop");
            if (dropped) {
                metrics.increment("logs");
                logger.info("Dropped normal logs");
                Thread.sleep(RandomUtils.random(300, 500));
                return true;
            }
        }

        // Drop oak logs
        if (InventoryInteraction.inventoryContains(OAK_LOGS_ID)) {
            int beforeDrop = InventoryInteraction.getInventoryItemCount(OAK_LOGS_ID);
            boolean dropped = InventoryInteraction.interactWithInventoryItem(OAK_LOGS_ID, "Drop");
            if (dropped) {
                int afterDrop = InventoryInteraction.getInventoryItemCount(OAK_LOGS_ID);
                int logsDropped = beforeDrop - afterDrop;
                metrics.increment("oak_logs", logsDropped);
                logger.info("Dropped oak logs");
                Thread.sleep(RandomUtils.random(300, 500));
                return true;
            }
        }

        // Drop willow logs
        if (InventoryInteraction.inventoryContains(WILLOW_LOGS_ID)) {
            int beforeDrop = InventoryInteraction.getInventoryItemCount(WILLOW_LOGS_ID);
            boolean dropped = InventoryInteraction.interactWithInventoryItem(WILLOW_LOGS_ID, "Drop");
            if (dropped) {
                int afterDrop = InventoryInteraction.getInventoryItemCount(WILLOW_LOGS_ID);
                int logsDropped = beforeDrop - afterDrop;
                metrics.increment("willow_logs", logsDropped);
                logger.info("Dropped willow logs");
                Thread.sleep(RandomUtils.random(300, 500));
                return true;
            }
        }

        // If we get here, no more logs to drop
        if (!hasLogsInInventory()) {
            currentState = State.CHOPPING;
        }

        return true;
    }

    @Override
    public void onStop() {
        logger.info("Power Chopper stopped");
    }

    @Override
    public java.util.Map<String, PaintMetric> getPaintMetrics() {
        if (metrics == null) {
            return new java.util.HashMap<>();
        }
        return metrics.generatePaintMetrics();
    }
}
