plugins {
    id 'java'
    id 'application'
    id 'org.jetbrains.kotlin.jvm' version '1.4.10'
}

mainClassName = 'rt4.client'
version = '1.0.0'

sourceCompatibility = 1.8
targetCompatibility = 1.8

compileJava.options.encoding = 'UTF-8'

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
    // Suppress warnings about deprecated Applet classes (expected for 2009scape client)
    options.compilerArgs += ['-Xlint:-removal', '-Xlint:-deprecation', '-Xlint:-unchecked']
}

dependencies {
    compileOnly project(':deob-annotations')
    implementation project(':signlink')
    implementation 'lib:gson'
    implementation 'lib:gluegen-rt'
    implementation 'lib:gluegen-rt-natives-windows-amd64'
    implementation 'lib:gluegen-rt-natives-windows-i586'
    implementation 'lib:gluegen-rt-natives-linux-amd64'
    implementation 'lib:gluegen-rt-natives-linux-i586'
    implementation 'lib:gluegen-rt-natives-macosx-universal'
    implementation 'lib:gluegen-rt-natives-android-aarch64'
    implementation 'lib:gluegen-rt-natives-linux-aarch64'

    implementation 'lib:jogl-all'
    implementation 'lib:jogl-all-natives-linux-aarch64'
    implementation 'lib:jogl-all-natives-windows-amd64'
    implementation 'lib:jogl-all-natives-windows-i586'
    implementation 'lib:jogl-all-natives-linux-amd64'
    implementation 'lib:jogl-all-natives-linux-i586'
    implementation 'lib:jogl-all-natives-macosx-universal'
    implementation 'lib:jogl-all-natives-android-aarch64'

    runtime 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10'
}

jar {
    manifest {
        attributes 'Main-Class': "$mainClassName"
    }
    from { configurations.compileClasspath.collect { it.isDirectory() ? it : zipTree(it) } }
    duplicatesStrategy = DuplicatesStrategy.INCLUDE
}
